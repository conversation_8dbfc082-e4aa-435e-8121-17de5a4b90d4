import { REPOSITORY_CONSTANTS } from "../utils/constants.util.js";
import { bulkCreate } from "../utils/database.util.js";
import { createLogger } from "../utils/logger.util.js";
import {
  AccountsReceivable,
  TreatmentPlanAnalysis,
  NoShowAppointments,
  AvgDailyProduction,
  NewPatients,
} from "../models/index.model.js";

const logger = createLogger(REPOSITORY_CONSTANTS.LOGGER_NAME);

/**
 * KPI Repository
 * @module kpiRepository
 * @description Repository for KPIs
 */
export const kpiRepository = {
  /**
   * Create a single account receivable record
   * @param {Object} data - Account receivable data
   * @returns {Promise<Object>} Created record
   */
  createAccountReceivable: async (data) => {
    try {
      logger.info(
        `Creating ${REPOSITORY_CONSTANTS.ACCOUNT_RECEIVABLES_ENTITY}`
      );
      return await bulkCreate(AccountsReceivable, data);
    } catch (error) {
      logger.error(
        `Error creating ${REPOSITORY_CONSTANTS.ACCOUNT_RECEIVABLES_ENTITY}`,
        { error: error.message }
      );
      throw error;
    }
  },
  /**
   * Create avg daily production records
   * @param {Object} data - Avg daily production data
   * @returns {Promise<Object>} Created record
   */
  createAvgDailyProduction: async (data) => {
    try {
      logger.info(
        `Creating ${REPOSITORY_CONSTANTS.AVG_DAILY_PRODUCTION_ENTITY}`
      );
      return await bulkCreate(AvgDailyProduction, data);
    } catch (error) {
      logger.error(
        `Error creating ${REPOSITORY_CONSTANTS.AVG_DAILY_PRODUCTION_ENTITY}`,
        { error: error.message }
      );
      throw error;
    }
  },
  /**
   * Create new patients records
   * @param {Object} data - New patients data
   * @returns {Promise<Object>} Created record
   */
  createNewPatients: async (data) => {
    try {
      logger.info(`Creating ${REPOSITORY_CONSTANTS.NEW_PATIENTS_ENTITY}`);
      return await bulkCreate(NewPatients, data);
    } catch (error) {
      logger.error(
        `Error creating ${REPOSITORY_CONSTANTS.NEW_PATIENTS_ENTITY}`,
        { error: error.message }
      );
      throw error;
    }
  },
  /**
   * Create no show appointments records
   * @param {Object} data - No show appointments data
   * @returns {Promise<Object>} Created record
   */
  createNoShowAppointments: async (data) => {
    try {
      logger.info(
        `Creating ${REPOSITORY_CONSTANTS.NO_SHOW_APPOINTMENTS_ENTITY}`
      );
      return await bulkCreate(NoShowAppointments, data);
    } catch (error) {
      logger.error(
        `Error creating ${REPOSITORY_CONSTANTS.NO_SHOW_APPOINTMENTS_ENTITY}`,
        { error: error.message }
      );
      throw error;
    }
  },
  /**
   * Create treatment analysis records
   * @param {Object} data - Treatment analysis data
   * @returns {Promise<Object>} Created record
   */
  createTreatmentAnalysis: async (data) => {
    try {
      logger.info(`Creating ${REPOSITORY_CONSTANTS.TREATMENT_ANALYSIS_ENTITY}`);
      return await bulkCreate(TreatmentPlanAnalysis, data);
    } catch (error) {
      logger.error(
        `Error creating ${REPOSITORY_CONSTANTS.TREATMENT_ANALYSIS_ENTITY}`,
        { error: error.message }
      );
      throw error;
    }
  },
};
