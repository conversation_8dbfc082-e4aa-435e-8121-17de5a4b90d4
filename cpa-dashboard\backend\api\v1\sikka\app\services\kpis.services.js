import { LOG_ACTIONS, SIKKA_API } from "../utils/constants.util.js";
import { createLogger } from "../utils/logger.util.js";
import {
  fetchKpiWithRequestKey,
  fetchAndStoreKpiData,
} from "../utils/methods.util.js";
import { generateRequestKey, getRequestKey } from "./sikka.service.js";
import { kpiRepository } from "../repositories/kpi.repository.js";

const logger = createLogger("kpis-service");

/**
 * Fetch account receivables with automatic request key management
 * @param {string} office_id - Office ID
 * @returns {Object} Account receivables data
 */
export const fetchAccountReceivables = async (office_id) => {
  return await fetchKpiWithRequestKey(
    office_id,
    null, // start_date not used for account receivables
    null, // end_date not used for account receivables
    fetchAndStoreAccountReceivables,
    getRequestKey,
    generateRequestKey,
    LOG_ACTIONS.ACCOUNT_RECEIVABLES_SUCCESS,
    LOG_ACTIONS.ACCOUNT_RECEIVABLES_FAILED,
    logger
  );
};

/**
 * Fetch account receivables from Sikka API and store in the database
 * @param {Object} headers - Request headers
 * @returns {Promise<Array>} Array of stored account receivables
 */
export const fetchAndStoreAccountReceivables = async (headers) => {
  return await fetchAndStoreKpiData(
    SIKKA_API.ENDPOINTS.ACCOUNT_RECEIVABLES,
    headers,
    null, // start_date not used for account receivables
    null, // end_date not used for account receivables
    kpiRepository.createAccountReceivable,
    LOG_ACTIONS.ACCOUNT_RECEIVABLES_SUCCESS,
    LOG_ACTIONS.ACCOUNT_RECEIVABLES_FAILED,
    logger
  );
};

/**
 * Fetch treatment analysis with automatic request key management
 * @param {string} office_id - Office ID
 * @returns {Object} Treatment analysis data
 */
export const fetchTreatmentAnalysis = async (
  office_id,
  start_date,
  end_date
) => {
  return await fetchKpiWithRequestKey(
    office_id,
    start_date,
    end_date,
    fetchAndStoreTreatmentAnalysis,
    getRequestKey,
    generateRequestKey,
    LOG_ACTIONS.TREATMENT_ANALYSIS_SUCCESS,
    LOG_ACTIONS.TREATMENT_ANALYSIS_FAILED,
    logger
  );
};

/**
 * Fetch treatment analysis from Sikka API and store in the database
 * @param {Object} headers - Request headers
 * @returns {Promise<Array>} Array of stored treatment analysis
 */
export const fetchAndStoreTreatmentAnalysis = async (
  headers,
  start_date,
  end_date
) => {
  return await fetchAndStoreKpiData(
    SIKKA_API.ENDPOINTS.TREATMENT_PLAN_ANALYSIS,
    headers,
    start_date,
    end_date,
    kpiRepository.createTreatmentAnalysis,
    LOG_ACTIONS.TREATMENT_ANALYSIS_SUCCESS,
    LOG_ACTIONS.TREATMENT_ANALYSIS_FAILED,
    logger
  );
};

/**
 * Fetch no show appointments with automatic request key management
 * @param {string} office_id - Office ID
 * @returns {Object} No show appointments data
 */
export const fetchNoShowAppointments = async (
  office_id,
  start_date,
  end_date
) => {
  return await fetchKpiWithRequestKey(
    office_id,
    start_date,
    end_date,
    fetchAndStoreNoShowAppointments,
    getRequestKey,
    generateRequestKey,
    LOG_ACTIONS.NO_SHOW_APPOINTMENTS_SUCCESS,
    LOG_ACTIONS.NO_SHOW_APPOINTMENTS_FAILED,
    logger
  );
};

/**
 * Fetch no show appointments from Sikka API and store in the database
 * @param {Object} headers - Request headers
 * @returns {Promise<Array>} Array of stored no show appointments
 */
export const fetchAndStoreNoShowAppointments = async (
  headers,
  start_date,
  end_date
) => {
  return await fetchAndStoreKpiData(
    SIKKA_API.ENDPOINTS.NO_SHOW_APPOINTMENTS,
    headers,
    start_date,
    end_date,
    kpiRepository.createNoShowAppointments,
    LOG_ACTIONS.NO_SHOW_APPOINTMENTS_SUCCESS,
    LOG_ACTIONS.NO_SHOW_APPOINTMENTS_FAILED,
    logger
  );
};

/**
 * Fetch avg daily production with automatic request key management
 * @param {string} office_id - Office ID
 * @returns {Object} Avg daily production data
 */
export const fetchAvgDailyProduction = async (
  office_id,
  start_date,
  end_date
) => {
  return await fetchKpiWithRequestKey(
    office_id,
    start_date,
    end_date,
    fetchAndStoreAvgDailyProduction,
    getRequestKey,
    generateRequestKey,
    LOG_ACTIONS.AVG_DAILY_PRODUCTION_SUCCESS,
    LOG_ACTIONS.AVG_DAILY_PRODUCTION_FAILED,
    logger
  );
};

/**
 * Fetch avg daily production from Sikka API and store in the database
 * @param {Object} headers - Request headers
 * @returns {Promise<Array>} Array of stored avg daily production
 */
export const fetchAndStoreAvgDailyProduction = async (
  headers,
  start_date,
  end_date
) => {
  return await fetchAndStoreKpiData(
    SIKKA_API.ENDPOINTS.AVG_DAILY_PRODUCTION,
    headers,
    start_date,
    end_date,
    kpiRepository.createAvgDailyProduction,
    LOG_ACTIONS.AVG_DAILY_PRODUCTION_SUCCESS,
    LOG_ACTIONS.AVG_DAILY_PRODUCTION_FAILED,
    logger
  );
};
/**
 * Fetch new patients with automatic request key management
 * @param {string} office_id - Office ID
 * @returns {Object} New patients data
 */
export const fetchNewPatients = async (office_id, start_date, end_date) => {
  return await fetchKpiWithRequestKey(
    office_id,
    start_date,
    end_date,
    fetchAndStoreNewPatients,
    getRequestKey,
    generateRequestKey,
    LOG_ACTIONS.NEW_PATIENTS_SUCCESS,
    LOG_ACTIONS.NEW_PATIENTS_FAILED,
    logger
  );
};

/**
 * Fetch new patients from Sikka API and store in the database
 * @param {Object} headers - Request headers
 * @returns {Promise<Array>} Array of stored new patients
 */
export const fetchAndStoreNewPatients = async (
  headers,
  start_date,
  end_date
) => {
  return await fetchAndStoreKpiData(
    SIKKA_API.ENDPOINTS.NEW_PATIENTS,
    headers,
    start_date,
    end_date,
    kpiRepository.createNewPatients,
    LOG_ACTIONS.NEW_PATIENTS_SUCCESS,
    LOG_ACTIONS.NEW_PATIENTS_FAILED,
    logger
  );
};
