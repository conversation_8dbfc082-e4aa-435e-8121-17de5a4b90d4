import {
  LOG_ACTIONS,
  SIKKA_CONSTANTS,
  MODEL_FIELDS,
  HTTP_HEADER_CONSTANTS,
  METHOD_TYPES,
  SIKKA_API,
  ERROR_MESSAGES,
  NUMERIC_CONSTANTS,
} from "../utils/constants.util.js";
import { createLogger } from "../utils/logger.util.js";
import { getKpiUrl, sikkaApiCall } from "../utils/methods.util.js";
import { generateRequestKey, getRequestKey } from "./sikka.service.js";
import { sikkaRepository } from "../repositories/sikka.repository.js";
import { kpiRepository } from "../repositories/kpi.repository.js";

const logger = createLogger("kpis-service");

/**
 * Fetch account receivables with automatic request key management
 * @param {string} office_id - Office ID
 * @returns {Object} Account receivables data
 */
export const fetchAccountReceivables = async (office_id) => {
  try {
    logger.info(LOG_ACTIONS.ACCOUNT_RECEIVABLES_SUCCESS);

    // Get current request key
    let { request_key, end_time } = await getRequestKey(office_id);

    // Check if request key is expired
    if (end_time < new Date()) {
      logger.info(SIKKA_CONSTANTS.REQUEST_KEY_EXPIRED_MESSAGE);
      const newKeyRecord = await generateRequestKey();
      request_key = newKeyRecord.get(MODEL_FIELDS.REQUEST_KEY);
    } else {
      logger.info(SIKKA_CONSTANTS.REQUEST_KEY_VALID_MESSAGE);
    }

    // Fetch and store account receivables
    const headers = { [HTTP_HEADER_CONSTANTS.REQUEST_KEY_HEADER]: request_key };
    console.log(headers);
    const response = await fetchAndStoreAccountReceivables(headers);

    logger.info(LOG_ACTIONS.ACCOUNT_RECEIVABLES_SUCCESS);
    return response;
  } catch (error) {
    logger.error(LOG_ACTIONS.ACCOUNT_RECEIVABLES_FAILED, {
      error: error.message,
    });
    throw error;
  }
};

/**
 * Fetch account receivables from Sikka API and store in the database
 * @param {Object} headers - Request headers
 * @returns {Promise<Array>} Array of stored account receivables
 */
export const fetchAndStoreAccountReceivables = async (headers) => {
  try {
    const response = await sikkaApiCall(
      METHOD_TYPES.GET,
      getKpiUrl(
        SIKKA_API.ENDPOINTS.ACCOUNT_RECEIVABLES,
        NUMERIC_CONSTANTS.PRACTICE_ID_DEFAULT
      ),
      {
        headers,
        successMessage: LOG_ACTIONS.ACCOUNT_RECEIVABLES_SUCCESS,
        errorMessage: ERROR_MESSAGES.ACCOUNT_RECEIVABLES_FAILED,
      }
    );

    if (response.items && response.items.length > 0) {
      // Store all account receivables in bulk
      return await kpiRepository.createAccountReceivable(response.items);
    }

    return [];
  } catch (error) {
    logger.error(LOG_ACTIONS.ACCOUNT_RECEIVABLES_FAILED, {
      error: error.message,
    });
    throw error;
  }
};

/**
 * Fetch treatment analysis with automatic request key management
 * @param {string} office_id - Office ID
 * @returns {Object} Treatment analysis data
 */
export const fetchTreatmentAnalysis = async (
  office_id,
  start_date,
  end_date
) => {
  try {
    logger.info(LOG_ACTIONS.TREATMENT_ANALYSIS_SUCCESS);

    // Get current request key
    let { request_key, end_time } = await getRequestKey(office_id);

    // Check if request key is expired
    if (end_time < new Date()) {
      logger.info(SIKKA_CONSTANTS.REQUEST_KEY_EXPIRED_MESSAGE);
      const newKeyRecord = await generateRequestKey();
      request_key = newKeyRecord.get(MODEL_FIELDS.REQUEST_KEY);
    } else {
      logger.info(SIKKA_CONSTANTS.REQUEST_KEY_VALID_MESSAGE);
    }

    // Fetch and store treatment analysis
    const headers = { [HTTP_HEADER_CONSTANTS.REQUEST_KEY_HEADER]: request_key };
    const response = await fetchAndStoreTreatmentAnalysis(
      headers,
      start_date,
      end_date
    );

    logger.info(LOG_ACTIONS.TREATMENT_ANALYSIS_SUCCESS);
    return response;
  } catch (error) {
    logger.error(LOG_ACTIONS.TREATMENT_ANALYSIS_FAILED, {
      error: error.message,
    });
    throw error;
  }
};

/**
 * Fetch treatment analysis from Sikka API and store in the database
 * @param {Object} headers - Request headers
 * @returns {Promise<Array>} Array of stored treatment analysis
 */
export const fetchAndStoreTreatmentAnalysis = async (
  headers,
  start_date,
  end_date
) => {
  try {
    const response = await sikkaApiCall(
      METHOD_TYPES.GET,
      getKpiUrl(
        SIKKA_API.ENDPOINTS.TREATMENT_PLAN_ANALYSIS,
        NUMERIC_CONSTANTS.PRACTICE_ID_DEFAULT,
        start_date,
        end_date
      ),
      {
        headers,
        successMessage: LOG_ACTIONS.TREATMENT_ANALYSIS_SUCCESS,
        errorMessage: ERROR_MESSAGES.ACCOUNT_RECEIVABLES_FAILED, // You may want to add a new error message for treatment analysis
      }
    );

    if (response.items && response.items.length > 0) {
      // Store all treatment analysis in bulk
      return await kpiRepository.createTreatmentAnalysis(response.items);
    }

    return [];
  } catch (error) {
    logger.error(LOG_ACTIONS.TREATMENT_ANALYSIS_FAILED, {
      error: error.message,
    });
    throw error;
  }
};

/**
 * Fetch no show appointments with automatic request key management
 * @param {string} office_id - Office ID
 * @returns {Object} No show appointments data
 */
export const fetchNoShowAppointments = async (
  office_id,
  start_date,
  end_date
) => {
  try {
    logger.info(LOG_ACTIONS.NO_SHOW_APPOINTMENTS_SUCCESS);

    // Get current request key
    let { request_key, end_time } = await getRequestKey(office_id);

    // Check if request key is expired
    if (end_time < new Date()) {
      logger.info(SIKKA_CONSTANTS.REQUEST_KEY_EXPIRED_MESSAGE);
      const newKeyRecord = await generateRequestKey();
      request_key = newKeyRecord.get(MODEL_FIELDS.REQUEST_KEY);
    } else {
      logger.info(SIKKA_CONSTANTS.REQUEST_KEY_VALID_MESSAGE);
    }

    // Fetch and store no show appointments
    const headers = { [HTTP_HEADER_CONSTANTS.REQUEST_KEY_HEADER]: request_key };
    const response = await fetchAndStoreNoShowAppointments(
      headers,
      start_date,
      end_date
    );

    logger.info(LOG_ACTIONS.NO_SHOW_APPOINTMENTS_SUCCESS);
    return response;
  } catch (error) {
    logger.error(LOG_ACTIONS.NO_SHOW_APPOINTMENTS_FAILED, {
      error: error.message,
    });
    throw error;
  }
};

/**
 * Fetch no show appointments from Sikka API and store in the database
 * @param {Object} headers - Request headers
 * @returns {Promise<Array>} Array of stored no show appointments
 */
export const fetchAndStoreNoShowAppointments = async (
  headers,
  start_date,
  end_date
) => {
  try {
    const response = await sikkaApiCall(
      METHOD_TYPES.GET,
      getKpiUrl(
        SIKKA_API.ENDPOINTS.NO_SHOW_APPOINTMENTS,
        NUMERIC_CONSTANTS.PRACTICE_ID_DEFAULT,
        start_date,
        end_date
      ),
      {
        headers,
        successMessage: LOG_ACTIONS.NO_SHOW_APPOINTMENTS_SUCCESS,
        errorMessage: ERROR_MESSAGES.ACCOUNT_RECEIVABLES_FAILED, // You may want to add a new error message for no show appointments
      }
    );

    if (response.items && response.items.length > 0) {
      // Store all no show appointments in bulk
      return await kpiRepository.createNoShowAppointments(response.items);
    }

    return [];
  } catch (error) {
    logger.error(LOG_ACTIONS.NO_SHOW_APPOINTMENTS_FAILED, {
      error: error.message,
    });
    throw error;
  }
};

/**
 * Fetch avg daily production with automatic request key management
 * @param {string} office_id - Office ID
 * @returns {Object} Avg daily production data
 */
export const fetchAvgDailyProduction = async (
  office_id,
  start_date,
  end_date
) => {
  try {
    logger.info(LOG_ACTIONS.AVG_DAILY_PRODUCTION_SUCCESS);

    // Get current request key
    let { request_key, end_time } = await getRequestKey(office_id);

    // Check if request key is expired
    if (end_time < new Date()) {
      logger.info(SIKKA_CONSTANTS.REQUEST_KEY_EXPIRED_MESSAGE);
      const newKeyRecord = await generateRequestKey();
      request_key = newKeyRecord.get(MODEL_FIELDS.REQUEST_KEY);
    } else {
      logger.info(SIKKA_CONSTANTS.REQUEST_KEY_VALID_MESSAGE);
    }

    // Fetch and store avg daily production
    const headers = { [HTTP_HEADER_CONSTANTS.REQUEST_KEY_HEADER]: request_key };
    const response = await fetchAndStoreAvgDailyProduction(
      headers,
      start_date,
      end_date
    );

    logger.info(LOG_ACTIONS.AVG_DAILY_PRODUCTION_SUCCESS);
    return response;
  } catch (error) {
    logger.error(LOG_ACTIONS.AVG_DAILY_PRODUCTION_FAILED, {
      error: error.message,
    });
    throw error;
  }
};

/**
 * Fetch avg daily production from Sikka API and store in the database
 * @param {Object} headers - Request headers
 * @returns {Promise<Array>} Array of stored avg daily production
 */
export const fetchAndStoreAvgDailyProduction = async (
  headers,
  start_date,
  end_date
) => {
  try {
    const response = await sikkaApiCall(
      METHOD_TYPES.GET,
      getKpiUrl(
        SIKKA_API.ENDPOINTS.AVG_DAILY_PRODUCTION,
        NUMERIC_CONSTANTS.PRACTICE_ID_DEFAULT,
        start_date,
        end_date
      ),
      {
        headers,
        successMessage: LOG_ACTIONS.AVG_DAILY_PRODUCTION_SUCCESS,
        errorMessage: ERROR_MESSAGES.ACCOUNT_RECEIVABLES_FAILED, // You may want to add a new error message for avg daily production
      }
    );

    if (response.items && response.items.length > 0) {
      // Store all avg daily production in bulk
      return await kpiRepository.createAvgDailyProduction(response.items);
    }

    return [];
  } catch (error) {
    logger.error(LOG_ACTIONS.AVG_DAILY_PRODUCTION_FAILED, {
      error: error.message,
    });
    throw error;
  }
};
/**
 * Fetch new patients with automatic request key management
 * @param {string} office_id - Office ID
 * @returns {Object} New patients data
 */
export const fetchNewPatients = async (office_id, start_date, end_date) => {
  try {
    logger.info(LOG_ACTIONS.NEW_PATIENTS_SUCCESS);

    // Get current request key
    let { request_key, end_time } = await getRequestKey(office_id);

    // Check if request key is expired
    if (end_time < new Date()) {
      logger.info(SIKKA_CONSTANTS.REQUEST_KEY_EXPIRED_MESSAGE);
      const newKeyRecord = await generateRequestKey();
      request_key = newKeyRecord.get(MODEL_FIELDS.REQUEST_KEY);
    } else {
      logger.info(SIKKA_CONSTANTS.REQUEST_KEY_VALID_MESSAGE);
    }

    // Fetch and store new patients
    const headers = { [HTTP_HEADER_CONSTANTS.REQUEST_KEY_HEADER]: request_key };
    const response = await fetchAndStoreNewPatients(
      headers,
      start_date,
      end_date
    );

    logger.info(LOG_ACTIONS.NEW_PATIENTS_SUCCESS);
    return response;
  } catch (error) {
    logger.error(LOG_ACTIONS.NEW_PATIENTS_FAILED, {
      error: error.message,
    });
    throw error;
  }
};

/**
 * Fetch new patients from Sikka API and store in the database
 * @param {Object} headers - Request headers
 * @returns {Promise<Array>} Array of stored new patients
 */
export const fetchAndStoreNewPatients = async (
  headers,
  start_date,
  end_date
) => {
  try {
    const response = await sikkaApiCall(
      METHOD_TYPES.GET,
      getKpiUrl(
        SIKKA_API.ENDPOINTS.NEW_PATIENTS,
        NUMERIC_CONSTANTS.PRACTICE_ID_DEFAULT,
        start_date,
        end_date
      ),
      {
        headers,
        successMessage: LOG_ACTIONS.NEW_PATIENTS_SUCCESS,
        errorMessage: ERROR_MESSAGES.ACCOUNT_RECEIVABLES_FAILED, // You may want to add a new error message for new patients
      }
    );

    if (response.items && response.items.length > 0) {
      // Store all new patients in bulk
      return await kpiRepository.createNewPatients(response.items);
    }

    return [];
  } catch (error) {
    logger.error(LOG_ACTIONS.NEW_PATIENTS_FAILED, {
      error: error.message,
    });
    throw error;
  }
};
