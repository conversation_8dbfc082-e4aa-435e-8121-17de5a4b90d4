import { Request<PERSON><PERSON> } from "../models/index.model.js";
import {
  create,
  findOne,
  update,
  findById,
  exists,
} from "../utils/database.util.js";
import { createLogger } from "../utils/logger.util.js";
import {
  REPOSITORY_CONSTANTS,
  LOGGER_NAMES_EXTENDED,
} from "../utils/constants.util.js";

const logger = createLogger(LOGGER_NAMES_EXTENDED.SIKKA_REPOSITORY);

export const sikkaRepository = {
  /**
   * Create a new Sikka request key record
   * @param {Object} params - { request_key, start_time, end_time, expires_in, office_id }
   * @returns {Promise<Object>} Created record
   */
  createRequestKey: async (params) => {
    try {
      logger.info(`Creating ${REPOSITORY_CONSTANTS.REQUEST_KEY_ENTITY}`, {
        office_id: params.office_id,
      });
      return await create(RequestKey, params);
    } catch (error) {
      logger.error(
        `Error creating ${REPOSITORY_CONSTANTS.REQUEST_KEY_ENTITY}`,
        { error: error.message }
      );
      throw error;
    }
  },

  /**
   * Find Sikka request key record by query
   * @param {Object} where - Where conditions
   * @returns {Promise<Object|null>} Found record or null
   */
  findRequestKey: async (where) => {
    try {
      logger.info(`Finding ${REPOSITORY_CONSTANTS.REQUEST_KEY_ENTITY}`, {
        where,
      });
      return await findOne(RequestKey, { where });
    } catch (error) {
      logger.error(`Error finding ${REPOSITORY_CONSTANTS.REQUEST_KEY_ENTITY}`, {
        error: error.message,
      });
      throw error;
    }
  },

  /**
   * Find Sikka request key record by ID
   * @param {number} id - Record ID
   * @returns {Promise<Object|null>} Found record or null
   */
  findRequestKeyById: async (id) => {
    try {
      logger.info(`Finding ${REPOSITORY_CONSTANTS.REQUEST_KEY_ENTITY} by ID`, {
        id,
      });
      return await findById(RequestKey, id);
    } catch (error) {
      logger.error(
        `Error finding ${REPOSITORY_CONSTANTS.REQUEST_KEY_ENTITY} by ID`,
        { error: error.message }
      );
      throw error;
    }
  },

  /**
   * Update Sikka request key record
   * @param {string} id - Record ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object|null>} Updated record or null
   */
  updateRequestKey: async (id, updateData) => {
    try {
      logger.info(`Updating ${REPOSITORY_CONSTANTS.REQUEST_KEY_ENTITY}`, {
        id,
      });
      return await update(RequestKey, id, updateData);
    } catch (error) {
      logger.error(
        `Error updating ${REPOSITORY_CONSTANTS.REQUEST_KEY_ENTITY}`,
        { error: error.message }
      );
      throw error;
    }
  },

  /**
   * Check if request key exists
   * @param {number} id - Record ID
   * @returns {Promise<boolean>} True if exists, false otherwise
   */
  requestKeyExists: async (id) => {
    try {
      return await exists(RequestKey, id);
    } catch (error) {
      logger.error(
        `Error checking ${REPOSITORY_CONSTANTS.REQUEST_KEY_ENTITY} existence`,
        { error: error.message }
      );
      throw error;
    }
  },
};
