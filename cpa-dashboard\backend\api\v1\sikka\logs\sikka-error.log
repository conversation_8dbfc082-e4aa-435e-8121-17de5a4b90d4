{"error":"Invalid credentials: App ID is required and must be a string, App Key is required and must be a string","label":"sikka-service","level":"error","message":"Failed to generate request key","service":"sikka-service","timestamp":"2025-09-11T07:07:18.059Z"}
{"error":"Invalid credentials: App ID is required and must be a string, App Key is required and must be a string","label":"sikka-service","level":"error","message":"Failed to generate request key","service":"sikka-service","timestamp":"2025-09-11T07:08:01.435Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","error":"Request failed with status code 502","label":"sikka-service","level":"error","message":"Failed to fetch authorized practices","service":"sikka-service","timestamp":"2025-09-11T07:08:20.425Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","error":"Sikka API Error: 502 - Bad Gateway","label":"sikka-service","level":"error","message":"Failed to generate request key","service":"sikka-service","timestamp":"2025-09-11T07:08:20.426Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","error":"Cannot destructure property 'office_id' of 'practice' as it is undefined.","label":"sikka-service","level":"error","message":"Failed to generate request key","service":"sikka-service","timestamp":"2025-09-11T08:56:08.695Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","error":"Cannot destructure property 'office_id' of 'practice' as it is undefined.","label":"sikka-service","level":"error","message":"Failed to generate request key","service":"sikka-service","timestamp":"2025-09-11T08:57:39.372Z"}
{"label":"sikka-server","level":"error","message":"Database connection error occurred","service":"sikka-service","timestamp":"2025-09-12T09:45:43.554Z"}
{"label":"sikka-server","level":"error","message":"Database connection error occurred","service":"sikka-service","timestamp":"2025-09-12T09:46:37.260Z"}
{"error":"connect ETIMEDOUT 34.224.249.207:443","label":"sikka-service","level":"error","message":"Request key API call failed","service":"sikka-service","timestamp":"2025-09-12T10:33:23.982Z"}
{"error":"Request failed with status code 405","label":"sikka-service","level":"error","message":"Request key API call failed","service":"sikka-service","timestamp":"2025-09-12T11:46:30.011Z"}
{"error":"Request failed with status code 405","label":"sikka-service","level":"error","message":"Request key API call failed","service":"sikka-service","timestamp":"2025-09-12T11:47:08.920Z"}
{"error":"Request failed with status code 405","label":"sikka-service","level":"error","message":"Request key API call failed","service":"sikka-service","timestamp":"2025-09-12T11:47:37.738Z"}
{"error":"Request failed with status code 401","label":"sikka-service","level":"error","message":"Request key API call failed","service":"sikka-service","timestamp":"2025-09-12T11:48:47.091Z"}
{"error":"Cannot read properties of undefined (reading 'request_key')","label":"sikka-controller","level":"error","message":"Failed to fetch account receivables","service":"sikka-service","timestamp":"2025-09-13T05:49:34.245Z"}
{"error":"Cannot read properties of undefined (reading 'request_key')","label":"sikka-controller","level":"error","message":"Failed to fetch account receivables","service":"sikka-service","timestamp":"2025-09-13T05:51:57.037Z"}
{"error":"Cannot read properties of undefined (reading 'request_key')","label":"sikka-controller","level":"error","message":"Failed to fetch account receivables","service":"sikka-service","timestamp":"2025-09-13T05:53:49.117Z"}
{"error":"invalid input syntax for type integer: \"86400 second(s)\"","label":"sikka-repository","level":"error","message":"Error updating RequestKey","service":"sikka-service","timestamp":"2025-09-13T06:30:47.253Z"}
{"error":"invalid input syntax for type integer: \"86400 second(s)\"","label":"sikka-service","level":"error","message":"Failed to generate request key","service":"sikka-service","timestamp":"2025-09-13T06:30:47.254Z"}
{"error":"invalid input syntax for type integer: \"86400 second(s)\"","label":"sikka-controller","level":"error","message":"Failed to generate request key","service":"sikka-service","timestamp":"2025-09-13T06:30:47.255Z"}
{"error":"Request failed with status code 404","label":"sikka-service","level":"error","message":"Request key API call failed","service":"sikka-service","timestamp":"2025-09-15T07:03:01.530Z"}
{"error":"Sikka API Error: 404 - Not Found","label":"kpis-service","level":"error","message":"Failed to fetch account receivables","service":"sikka-service","timestamp":"2025-09-15T07:03:01.531Z"}
{"error":"Sikka API Error: 404 - Not Found","level":"error","message":"Failed to fetch account receivables","service":"sikka-service","timestamp":"2025-09-15T07:03:01.532Z"}
{"error":"Sikka API Error: 404 - Not Found","label":"kpis-service","level":"error","message":"Failed to fetch account receivables","service":"sikka-service","timestamp":"2025-09-15T07:03:01.532Z"}
{"error":"Request failed with status code 400","label":"sikka-service","level":"error","message":"Request key API call failed","service":"sikka-service","timestamp":"2025-09-15T07:04:18.507Z"}
{"error":"Sikka API Error: 400 - Bad Request","label":"kpis-service","level":"error","message":"Failed to fetch account receivables","service":"sikka-service","timestamp":"2025-09-15T07:04:18.508Z"}
{"error":"Sikka API Error: 400 - Bad Request","level":"error","message":"Failed to fetch account receivables","service":"sikka-service","timestamp":"2025-09-15T07:04:18.509Z"}
{"error":"Sikka API Error: 400 - Bad Request","label":"kpis-service","level":"error","message":"Failed to fetch account receivables","service":"sikka-service","timestamp":"2025-09-15T07:04:18.509Z"}
{"error":"Request failed with status code 400","label":"sikka-service","level":"error","message":"Request key API call failed","service":"sikka-service","timestamp":"2025-09-15T07:05:46.685Z"}
{"error":"Sikka API Error: 400 - Bad Request","label":"kpis-service","level":"error","message":"Failed to fetch account receivables","service":"sikka-service","timestamp":"2025-09-15T07:05:46.686Z"}
{"error":"Sikka API Error: 400 - Bad Request","level":"error","message":"Failed to fetch account receivables","service":"sikka-service","timestamp":"2025-09-15T07:05:46.686Z"}
{"error":"Sikka API Error: 400 - Bad Request","label":"kpis-service","level":"error","message":"Failed to fetch account receivables","service":"sikka-service","timestamp":"2025-09-15T07:05:46.686Z"}
{"error":"Request failed with status code 400","label":"sikka-service","level":"error","message":"Request key API call failed","service":"sikka-service","timestamp":"2025-09-15T07:07:55.878Z"}
{"error":"Sikka API Error: 400 - Bad Request","label":"kpis-service","level":"error","message":"Failed to fetch account receivables","service":"sikka-service","timestamp":"2025-09-15T07:07:55.879Z"}
{"error":"Sikka API Error: 400 - Bad Request","level":"error","message":"Failed to fetch account receivables","service":"sikka-service","timestamp":"2025-09-15T07:07:55.880Z"}
{"error":"Sikka API Error: 400 - Bad Request","label":"kpis-service","level":"error","message":"Failed to fetch account receivables","service":"sikka-service","timestamp":"2025-09-15T07:07:55.880Z"}
{"error":"Request failed with status code 400","label":"sikka-service","level":"error","message":"Request key API call failed","service":"sikka-service","timestamp":"2025-09-15T07:10:22.884Z"}
{"error":"Sikka API Error: 400 - Bad Request","label":"kpis-service","level":"error","message":"Failed to fetch account receivables","service":"sikka-service","timestamp":"2025-09-15T07:10:22.884Z"}
{"error":"Sikka API Error: 400 - Bad Request","level":"error","message":"Failed to fetch account receivables","service":"sikka-service","timestamp":"2025-09-15T07:10:22.885Z"}
{"error":"Sikka API Error: 400 - Bad Request","label":"kpis-service","level":"error","message":"Failed to fetch account receivables","service":"sikka-service","timestamp":"2025-09-15T07:10:22.885Z"}
{"error":"Request failed with status code 400","label":"sikka-service","level":"error","message":"Request key API call failed","service":"sikka-service","timestamp":"2025-09-15T07:11:53.482Z"}
{"error":"Sikka API Error: 400 - Bad Request","label":"kpis-service","level":"error","message":"Failed to fetch account receivables","service":"sikka-service","timestamp":"2025-09-15T07:11:53.482Z"}
{"error":"Sikka API Error: 400 - Bad Request","level":"error","message":"Failed to fetch account receivables","service":"sikka-service","timestamp":"2025-09-15T07:11:53.483Z"}
{"error":"Sikka API Error: 400 - Bad Request","label":"kpis-service","level":"error","message":"Failed to fetch account receivables","service":"sikka-service","timestamp":"2025-09-15T07:11:53.483Z"}
{"error":"WHERE parameter \"office_id\" has invalid \"undefined\" value","label":"sikka-repository","level":"error","message":"Error finding RequestKey","service":"sikka-service","timestamp":"2025-09-15T08:51:35.143Z"}
{"error":"WHERE parameter \"office_id\" has invalid \"undefined\" value","label":"kpis-service","level":"error","message":"Failed to fetch treatment analysis","service":"sikka-service","timestamp":"2025-09-15T08:51:35.144Z"}
{"error":"WHERE parameter \"office_id\" has invalid \"undefined\" value","level":"error","message":"Failed to fetch treatment analysis","service":"sikka-service","timestamp":"2025-09-15T08:51:35.145Z"}
{"error":"Request failed with status code 400","label":"sikka-service","level":"error","message":"Request key API call failed","service":"sikka-service","timestamp":"2025-09-15T08:52:21.787Z"}
{"error":"Sikka API Error: 400 - Bad Request","label":"kpis-service","level":"error","message":"Failed to fetch treatment analysis","service":"sikka-service","timestamp":"2025-09-15T08:52:21.789Z"}
{"error":"Sikka API Error: 400 - Bad Request","level":"error","message":"Failed to fetch treatment analysis","service":"sikka-service","timestamp":"2025-09-15T08:52:21.790Z"}
{"error":"Sikka API Error: 400 - Bad Request","label":"kpis-service","level":"error","message":"Failed to fetch treatment analysis","service":"sikka-service","timestamp":"2025-09-15T08:52:21.790Z"}
{"error":"Request failed with status code 400","label":"sikka-service","level":"error","message":"Request key API call failed","service":"sikka-service","timestamp":"2025-09-15T08:59:44.682Z"}
{"error":"Sikka API Error: 400 - Bad Request","label":"kpis-service","level":"error","message":"Failed to fetch treatment analysis","service":"sikka-service","timestamp":"2025-09-15T08:59:44.683Z"}
{"error":"Sikka API Error: 400 - Bad Request","level":"error","message":"Failed to fetch treatment analysis","service":"sikka-service","timestamp":"2025-09-15T08:59:44.684Z"}
{"error":"Sikka API Error: 400 - Bad Request","label":"kpis-service","level":"error","message":"Failed to fetch treatment analysis","service":"sikka-service","timestamp":"2025-09-15T08:59:44.683Z"}
{"error":"Request failed with status code 400","label":"sikka-service","level":"error","message":"Request key API call failed","service":"sikka-service","timestamp":"2025-09-15T09:50:46.742Z"}
{"error":"Sikka API Error: 400 - Bad Request","label":"kpis-service","level":"error","message":"Failed to fetch avg daily production","service":"sikka-service","timestamp":"2025-09-15T09:50:46.743Z"}
{"error":"Sikka API Error: 400 - Bad Request","level":"error","message":"Failed to fetch avg daily production","service":"sikka-service","timestamp":"2025-09-15T09:50:46.745Z"}
{"error":"Sikka API Error: 400 - Bad Request","label":"kpis-service","level":"error","message":"Failed to fetch avg daily production","service":"sikka-service","timestamp":"2025-09-15T09:50:46.744Z"}
