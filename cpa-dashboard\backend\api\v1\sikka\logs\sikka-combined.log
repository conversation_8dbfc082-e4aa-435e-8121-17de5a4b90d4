{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T07:04:27.650Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T07:04:27.656Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T07:04:27.656Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T07:04:27.657Z"}
{"errors":[{"field":"app_id","location":"body","message":"App ID is required"},{"field":"app_id","location":"body","message":"App ID must be between 1 and 100 characters"},{"field":"app_key","location":"body","message":"App Key is required"},{"field":"app_key","location":"body","message":"App Key must be between 1 and 255 characters"}],"label":"validation-middleware","level":"warn","message":"Validation failed","method":"POST","path":"/request-key","service":"sikka-service","timestamp":"2025-09-11T07:05:00.438Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T07:06:05.463Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T07:06:05.472Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T07:06:05.474Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T07:06:05.474Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T07:06:38.111Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T07:06:38.119Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T07:06:38.120Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T07:06:38.120Z"}
{"errors":[{"field":"app_id","location":"body","message":"App ID is required"},{"field":"app_id","location":"body","message":"App ID must be between 1 and 100 characters"},{"field":"app_key","location":"body","message":"App Key is required"},{"field":"app_key","location":"body","message":"App Key must be between 1 and 255 characters"}],"label":"validation-middleware","level":"warn","message":"Validation failed","method":"POST","path":"/request-key","service":"sikka-service","timestamp":"2025-09-11T07:06:42.259Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T07:07:14.006Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T07:07:14.013Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T07:07:14.014Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T07:07:14.014Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T07:07:18.056Z"}
{"label":"sikka-service","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T07:07:18.058Z"}
{"error":"Invalid credentials: App ID is required and must be a string, App Key is required and must be a string","label":"sikka-service","level":"error","message":"Failed to generate request key","service":"sikka-service","timestamp":"2025-09-11T07:07:18.059Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T07:07:56.081Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T07:07:56.103Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T07:07:56.104Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T07:07:56.105Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T07:08:01.432Z"}
{"label":"sikka-service","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T07:08:01.434Z"}
{"error":"Invalid credentials: App ID is required and must be a string, App Key is required and must be a string","label":"sikka-service","level":"error","message":"Failed to generate request key","service":"sikka-service","timestamp":"2025-09-11T07:08:01.435Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T07:08:19.240Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T07:08:19.241Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Fetching authorized practices from Sikka","service":"sikka-service","timestamp":"2025-09-11T07:08:19.242Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","error":"Request failed with status code 502","label":"sikka-service","level":"error","message":"Failed to fetch authorized practices","service":"sikka-service","timestamp":"2025-09-11T07:08:20.425Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","error":"Sikka API Error: 502 - Bad Gateway","label":"sikka-service","level":"error","message":"Failed to generate request key","service":"sikka-service","timestamp":"2025-09-11T07:08:20.426Z"}
{"label":"sikka-server","level":"info","message":"SIGINT received, shutting down gracefully","service":"sikka-service","timestamp":"2025-09-11T07:09:31.718Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T08:56:05.154Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T08:56:05.160Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T08:56:05.161Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T08:56:05.161Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T08:56:07.534Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T08:56:07.536Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Fetching authorized practices from Sikka","service":"sikka-service","timestamp":"2025-09-11T08:56:07.537Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","count":0,"label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","timestamp":"2025-09-11T08:56:08.694Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","error":"Cannot destructure property 'office_id' of 'practice' as it is undefined.","label":"sikka-service","level":"error","message":"Failed to generate request key","service":"sikka-service","timestamp":"2025-09-11T08:56:08.695Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T08:57:06.763Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T08:57:06.771Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T08:57:06.772Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T08:57:06.772Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T08:57:19.382Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T08:57:19.398Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T08:57:19.399Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T08:57:19.400Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T08:57:38.322Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T08:57:38.324Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Fetching authorized practices from Sikka","service":"sikka-service","timestamp":"2025-09-11T08:57:38.325Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","count":0,"label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","timestamp":"2025-09-11T08:57:39.370Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","error":"Cannot destructure property 'office_id' of 'practice' as it is undefined.","label":"sikka-service","level":"error","message":"Failed to generate request key","service":"sikka-service","timestamp":"2025-09-11T08:57:39.372Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T08:58:19.886Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T08:58:19.900Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T08:58:19.901Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T08:58:19.902Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T08:58:30.886Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T08:58:30.888Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Fetching authorized practices from Sikka","service":"sikka-service","timestamp":"2025-09-11T08:58:30.889Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","count":0,"label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","timestamp":"2025-09-11T08:58:31.896Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-11T08:58:32.228Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:01:12.312Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:01:12.341Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:01:12.342Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:01:12.343Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:02:14.962Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:02:14.968Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:02:14.969Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:02:14.969Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:02:26.622Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:02:26.642Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:02:26.643Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:02:26.644Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T09:02:58.896Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T09:02:58.899Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Fetching authorized practices from Sikka","service":"sikka-service","timestamp":"2025-09-11T09:02:58.900Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","count":0,"label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","timestamp":"2025-09-11T09:02:59.951Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-11T09:03:00.346Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:04:00.205Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:04:00.212Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:04:00.213Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:04:00.214Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:04:10.464Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:04:10.473Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:04:10.474Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:04:10.475Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:04:53.306Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:04:53.323Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:04:53.324Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:04:53.324Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T09:04:57.341Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T09:04:57.343Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Fetching authorized practices from Sikka","service":"sikka-service","timestamp":"2025-09-11T09:04:57.344Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","count":0,"label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","timestamp":"2025-09-11T09:04:58.464Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-11T09:04:58.804Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:06:44.318Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:06:44.324Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:06:44.325Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:06:44.325Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:07:27.638Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:07:27.647Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:07:27.648Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:07:27.649Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:07:49.181Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:07:49.188Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:07:49.189Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:07:49.189Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:08:02.703Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:08:02.732Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:08:02.733Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:08:02.733Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T09:08:25.779Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T09:08:25.781Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Fetching authorized practices from Sikka","service":"sikka-service","timestamp":"2025-09-11T09:08:25.782Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","count":0,"label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","timestamp":"2025-09-11T09:08:26.895Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-11T09:08:27.232Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:09:13.214Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:09:13.220Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:09:13.221Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:09:13.221Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:09:28.212Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:09:28.218Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:09:28.219Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:09:28.219Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:10:07.686Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:10:07.707Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:10:07.708Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:10:07.709Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:10:25.322Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:10:25.354Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:10:25.356Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:10:25.358Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:11:07.716Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:11:07.733Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:11:07.734Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:11:07.735Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T09:11:12.367Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T09:11:12.369Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Fetching authorized practices from Sikka","service":"sikka-service","timestamp":"2025-09-11T09:11:12.370Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","count":0,"label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","timestamp":"2025-09-11T09:11:13.432Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-11T09:11:13.772Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:11:40.727Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:11:40.735Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:11:40.736Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:11:40.737Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:11:58.959Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:11:58.981Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:11:58.983Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:11:58.983Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:12:11.987Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:12:11.999Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:12:12.000Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:12:12.000Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T09:12:15.687Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T09:12:15.689Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Fetching authorized practices from Sikka","service":"sikka-service","timestamp":"2025-09-11T09:12:15.691Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","count":0,"label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","timestamp":"2025-09-11T09:12:16.739Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-11T09:12:17.126Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:13:40.720Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:13:40.736Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:13:40.736Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:13:40.737Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T09:13:42.928Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T09:13:42.930Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Fetching authorized practices from Sikka","service":"sikka-service","timestamp":"2025-09-11T09:13:42.931Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","count":0,"label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","timestamp":"2025-09-11T09:13:44.145Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-11T09:13:44.489Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:14:09.108Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:14:09.116Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:14:09.117Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:14:09.117Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:14:22.709Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:14:22.730Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:14:22.731Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:14:22.732Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:14:59.808Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:14:59.822Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:14:59.823Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:14:59.823Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T09:15:02.457Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T09:15:02.460Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Fetching authorized practices from Sikka","service":"sikka-service","timestamp":"2025-09-11T09:15:02.461Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","count":0,"label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","timestamp":"2025-09-11T09:15:03.613Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-11T09:15:03.941Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:15:28.459Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:15:28.467Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:15:28.468Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:15:28.469Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:16:15.126Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:16:15.152Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:16:15.153Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:16:15.153Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T09:16:21.133Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T09:16:21.136Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Fetching authorized practices from Sikka","service":"sikka-service","timestamp":"2025-09-11T09:16:21.137Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","count":0,"label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","timestamp":"2025-09-11T09:16:22.329Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-11T09:16:22.653Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:16:51.842Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:16:51.854Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:16:51.855Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:16:51.855Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:16:59.247Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:16:59.263Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:16:59.265Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:16:59.266Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T09:17:04.743Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T09:17:04.746Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Fetching authorized practices from Sikka","service":"sikka-service","timestamp":"2025-09-11T09:17:04.748Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","count":0,"label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","timestamp":"2025-09-11T09:17:06.077Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-11T09:17:06.418Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:17:29.047Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:17:29.057Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:17:29.058Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:17:29.058Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:17:40.749Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:17:40.763Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:17:40.764Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:17:40.765Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:17:56.646Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:17:56.680Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:17:56.680Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:17:56.681Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:18:10.291Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:18:10.299Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:18:10.300Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:18:10.300Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:18:22.540Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:18:22.569Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:18:22.570Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:18:22.570Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:25:10.926Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:25:10.934Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:25:10.935Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:25:10.936Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:26:23.288Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:26:23.295Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:26:23.296Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:26:23.297Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:26:39.579Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:26:39.587Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:26:39.588Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:26:39.588Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:27:01.600Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:27:01.608Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:27:01.610Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:27:01.613Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:27:24.112Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:27:24.120Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:27:24.121Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:27:24.121Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:29:53.659Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:29:53.666Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:29:53.667Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:29:53.667Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:30:11.421Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:30:11.460Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:30:11.462Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:30:11.462Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:31:34.269Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:31:34.291Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:31:34.292Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:31:34.293Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:32:06.964Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:32:06.971Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:32:06.972Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:32:06.972Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:33:04.360Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:33:04.368Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:33:04.369Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:33:04.369Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:33:46.175Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:33:46.183Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:33:46.183Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:33:46.184Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:34:11.115Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:34:11.130Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:34:11.131Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:34:11.132Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:34:57.559Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:34:57.567Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:34:57.568Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:34:57.569Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:35:16.677Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:35:16.694Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:35:16.695Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:35:16.696Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:36:02.625Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:36:02.645Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:36:02.646Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:36:02.647Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:36:43.875Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:36:43.884Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:36:43.885Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:36:43.885Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:37:35.289Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:37:35.311Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:37:35.312Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:37:35.312Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:38:00.632Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:38:00.639Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:38:00.640Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:38:00.641Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:38:53.667Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:38:53.687Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:38:53.688Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:38:53.688Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:39:12.399Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:39:12.406Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:39:12.407Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:39:12.408Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:39:23.388Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:39:23.397Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:39:23.398Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:39:23.398Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:39:49.913Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:39:49.927Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:39:49.928Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:39:49.928Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:40:31.379Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:40:31.411Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:40:31.412Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:40:31.414Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:41:34.100Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:41:34.114Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:41:34.115Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:41:34.116Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:41:48.519Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:41:48.539Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:41:48.541Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:41:48.542Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:42:25.298Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:42:25.307Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:42:25.307Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:42:25.308Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:42:39.904Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:42:39.929Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:42:39.929Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:42:39.930Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:43:40.970Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:43:40.989Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:43:40.990Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:43:40.990Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T09:43:44.196Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Fetching authorized practices from Sikka","service":"sikka-service","timestamp":"2025-09-11T09:43:44.198Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","count":0,"label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","timestamp":"2025-09-11T09:43:45.554Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:44:03.357Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:44:03.377Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:44:03.378Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:44:03.379Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T09:44:07.625Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Fetching authorized practices from Sikka","service":"sikka-service","timestamp":"2025-09-11T09:44:07.627Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","count":0,"label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","timestamp":"2025-09-11T09:44:08.804Z"}
{"label":"sikka-controller","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-11T09:44:09.136Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:44:34.001Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:44:34.020Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:44:34.021Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:44:34.021Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T09:44:37.157Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Fetching authorized practices from Sikka","service":"sikka-service","timestamp":"2025-09-11T09:44:37.158Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","count":0,"label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","timestamp":"2025-09-11T09:44:38.439Z"}
{"label":"sikka-controller","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-11T09:44:38.765Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:47:53.463Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:47:53.470Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:47:53.471Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:47:53.472Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:48:04.562Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:48:04.575Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:48:04.577Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:48:04.577Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:49:38.130Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:49:38.138Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:49:38.139Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:49:38.140Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:50:16.481Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:50:16.498Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:50:16.499Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:50:16.500Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:50:31.699Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:50:31.714Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:50:31.715Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:50:31.716Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:50:46.996Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:50:47.002Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:50:47.003Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:50:47.004Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:51:41.389Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:51:41.397Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:51:41.398Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:51:41.398Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T09:52:43.370Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T09:52:43.393Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T09:52:43.394Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T09:52:43.395Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:08:47.686Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:08:47.693Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:08:47.694Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:08:47.695Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T10:08:51.534Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-11T10:08:51.537Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T10:11:16.480Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-11T10:11:16.481Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:11:45.902Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:11:45.914Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:11:45.915Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:11:45.915Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:12:03.901Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:12:03.915Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:12:03.916Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:12:03.916Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T10:12:07.643Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-11T10:12:07.645Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:12:38.377Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:12:38.393Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:12:38.394Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:12:38.395Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T10:12:47.497Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-11T10:12:47.500Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:13:20.004Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:13:20.031Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:13:20.032Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:13:20.033Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:13:30.382Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:13:30.390Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:13:30.391Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:13:30.392Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T10:13:34.678Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-11T10:13:34.680Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:14:29.219Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:14:29.228Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:14:29.230Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:14:29.230Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T10:14:35.437Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-11T10:14:35.438Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:15:58.959Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:15:58.967Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:15:58.968Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:15:58.969Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:16:13.491Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:16:13.503Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:16:13.504Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:16:13.504Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T10:16:16.295Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-11T10:16:16.297Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:16:36.158Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:16:36.165Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:16:36.166Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:16:36.166Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T10:16:50.141Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-11T10:16:50.142Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:17:28.000Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:17:28.008Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:17:28.009Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:17:28.010Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:17:54.449Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:17:54.468Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:17:54.469Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:17:54.470Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T10:17:57.974Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-11T10:17:57.977Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:18:42.720Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:18:42.729Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:18:42.731Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:18:42.731Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:19:05.848Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:19:05.868Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:19:05.869Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:19:05.870Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T10:19:08.688Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-11T10:19:08.690Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:19:52.869Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:19:52.896Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:19:52.897Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:19:52.897Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T10:19:55.238Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-11T10:19:55.240Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:20:20.369Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:20:20.376Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:20:20.377Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:20:20.378Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:20:32.445Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:20:32.455Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:20:32.456Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:20:32.457Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:20:47.084Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:20:47.099Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:20:47.100Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:20:47.101Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:21:14.829Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:21:14.843Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:21:14.844Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:21:14.845Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T10:21:17.423Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-11T10:21:17.425Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:22:04.038Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:22:04.057Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:22:04.058Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:22:04.058Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:22:33.253Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:22:33.268Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:22:33.269Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:22:33.270Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:23:14.896Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:23:14.929Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:23:14.930Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:23:14.930Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T10:23:19.050Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-11T10:23:19.052Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-11T10:23:20.387Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:23:39.474Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:23:39.482Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:23:39.483Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:23:39.483Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:23:47.782Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:23:47.804Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:23:47.805Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:23:47.806Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:24:28.131Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:24:28.146Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:24:28.147Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:24:28.148Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T10:24:51.227Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-11T10:24:51.229Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-11T10:24:52.389Z"}
{"label":"sikka-service","level":"info","message":"Calling POST /request_key","service":"sikka-service","timestamp":"2025-09-11T10:24:52.395Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","service":"sikka-service","status":200,"timestamp":"2025-09-11T10:24:52.732Z"}
{"label":"sikka-controller","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-11T10:24:52.735Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:25:22.377Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:25:22.386Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:25:22.387Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:25:22.387Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:25:51.072Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:25:51.088Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:25:51.089Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:25:51.089Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T10:25:55.652Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-11T10:25:55.653Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-11T10:25:56.844Z"}
{"label":"sikka-service","level":"info","message":"Calling POST /request_key","service":"sikka-service","timestamp":"2025-09-11T10:25:56.845Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","service":"sikka-service","status":200,"timestamp":"2025-09-11T10:25:57.176Z"}
{"label":"sikka-controller","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-11T10:25:57.178Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:26:15.764Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:26:15.771Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:26:15.772Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:26:15.772Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:26:34.931Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:26:35.018Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:26:35.026Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:26:35.038Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:28:45.824Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:28:45.830Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:28:45.831Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:28:45.832Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:32:20.682Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:32:20.691Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:32:20.692Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:32:20.692Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:32:37.381Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:32:37.389Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:32:37.390Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:32:37.391Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:32:48.333Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:32:48.340Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:32:48.340Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:32:48.341Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:33:02.372Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:33:02.380Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:33:02.381Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:33:02.381Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:33:14.661Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:33:14.668Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:33:14.669Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:33:14.669Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:33:26.807Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:33:26.829Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:33:26.830Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:33:26.831Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:33:37.683Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:33:37.698Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:33:37.699Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:33:37.699Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:33:47.692Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:33:47.700Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:33:47.702Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:33:47.706Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:34:08.843Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:34:08.853Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:34:08.854Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:34:08.854Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:34:25.877Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:34:25.885Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:34:25.886Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:34:25.886Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:34:39.817Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:34:39.827Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:34:39.828Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:34:39.829Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:35:03.102Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:35:03.110Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:35:03.111Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:35:03.111Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:37:31.667Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:37:31.678Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:37:31.679Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:37:31.679Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:37:44.309Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:37:44.318Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:37:44.319Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:37:44.320Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:38:06.846Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:38:06.856Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:38:06.856Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:38:06.857Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:38:20.096Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:38:20.104Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:38:20.105Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:38:20.105Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:38:32.911Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:38:32.919Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:38:32.920Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:38:32.921Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:38:46.768Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:38:46.777Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:38:46.778Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:38:46.778Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:39:00.488Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:39:00.500Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:39:00.501Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:39:00.501Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:39:11.518Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:39:11.526Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:39:11.527Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:39:11.528Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:39:22.787Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:39:22.795Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:39:22.796Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:39:22.796Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:41:08.144Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:41:08.150Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:41:08.151Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:41:08.151Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:42:03.585Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:42:03.623Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:42:03.624Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:42:03.624Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:42:22.617Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:42:22.622Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:42:22.622Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:42:22.623Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:42:46.465Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:42:46.482Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:42:46.483Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:42:46.484Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:43:25.082Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:43:25.089Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:43:25.090Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:43:25.090Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:43:37.097Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:43:37.125Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:43:37.126Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:43:37.127Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:44:39.425Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:44:39.430Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:44:39.431Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:44:39.431Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:44:51.404Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:44:51.435Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:44:51.436Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:44:51.436Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:46:03.671Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:46:03.685Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:46:03.686Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:46:03.686Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:49:00.241Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:49:00.252Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:49:00.253Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:49:00.254Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:49:05.880Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:49:05.887Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:49:05.888Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:49:05.889Z"}
{"label":"sikka-server","level":"info","message":"🚀 Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:50:52.020Z"}
{"label":"sikka-server","level":"info","message":"📊 Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:50:52.037Z"}
{"label":"sikka-server","level":"info","message":"🔗 API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:50:52.038Z"}
{"label":"sikka-server","level":"info","message":"🌍 Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:50:52.038Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T10:58:03.207Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T10:58:03.227Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T10:58:03.228Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-11T10:58:03.229Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T11:14:31.864Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T11:14:31.871Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T11:14:31.872Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-11T11:14:31.873Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T11:14:56.623Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T11:14:56.639Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T11:14:56.640Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-11T11:14:56.641Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T11:15:48.407Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T11:15:48.413Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T11:15:48.414Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-11T11:15:48.414Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T11:15:54.347Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-11T11:15:54.348Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-11T11:15:56.324Z"}
{"label":"sikka-service","level":"info","message":"Calling POST /request_key","service":"sikka-service","timestamp":"2025-09-11T11:15:56.325Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","service":"sikka-service","status":200,"timestamp":"2025-09-11T11:15:56.674Z"}
{"label":"sikka-controller","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-11T11:15:56.675Z"}
{"label":"sikka-server","level":"info","message":"SIGINT received, shutting down gracefully","service":"sikka-service","timestamp":"2025-09-11T11:16:57.657Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T11:22:40.702Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T11:22:40.709Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T11:22:40.710Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-11T11:22:40.710Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T11:22:51.527Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-11T11:22:51.528Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-11T11:22:52.705Z"}
{"label":"sikka-service","level":"info","message":"Calling POST /request_key","service":"sikka-service","timestamp":"2025-09-11T11:22:52.706Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","service":"sikka-service","status":200,"timestamp":"2025-09-11T11:22:53.177Z"}
{"label":"sikka-controller","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-11T11:22:53.178Z"}
{"label":"sikka-server","level":"info","message":"SIGINT received, shutting down gracefully","service":"sikka-service","timestamp":"2025-09-11T11:22:59.620Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T12:20:56.601Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T12:20:56.606Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T12:20:56.606Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-11T12:20:56.607Z"}
{"label":"sikka-server","level":"warn","message":"Route not found","method":"POST","service":"sikka-service","timestamp":"2025-09-11T12:21:04.571Z","url":"/api/v1/sikka/request-ke"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-11T12:21:31.865Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-11T12:21:31.867Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-11T12:21:33.075Z"}
{"label":"sikka-service","level":"info","message":"Calling POST /request_key","service":"sikka-service","timestamp":"2025-09-11T12:21:33.077Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","service":"sikka-service","status":200,"timestamp":"2025-09-11T12:21:33.437Z"}
{"label":"sikka-controller","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-11T12:21:33.437Z"}
{"label":"sikka-server","level":"info","message":"SIGINT received, shutting down gracefully","service":"sikka-service","timestamp":"2025-09-11T12:22:10.957Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-11T12:23:35.791Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-11T12:23:35.799Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-11T12:23:35.800Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-11T12:23:35.800Z"}
{"label":"sikka-server","level":"info","message":"SIGINT received, shutting down gracefully","service":"sikka-service","timestamp":"2025-09-11T12:23:46.063Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T05:12:33.476Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T05:12:33.482Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T05:12:33.483Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T05:12:33.483Z"}
{"label":"sikka-server","level":"warn","message":"Route not found","method":"POST","service":"sikka-service","timestamp":"2025-09-12T05:13:59.626Z","url":"/api/v1/tenant"}
{"label":"sikka-server","level":"info","message":"SIGINT received, shutting down gracefully","service":"sikka-service","timestamp":"2025-09-12T05:15:45.253Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T09:38:56.676Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T09:38:57.059Z"}
{"label":"sikka-server","level":"info","service":"sikka-service","timestamp":"2025-09-12T09:38:57.062Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T09:38:57.065Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T09:38:57.066Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T09:38:57.066Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T09:38:57.067Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T09:39:33.766Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T09:39:34.281Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-12T09:39:34.283Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T09:39:34.287Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T09:39:34.287Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T09:39:34.288Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T09:39:34.289Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T09:41:15.636Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T09:41:16.109Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-12T09:41:16.111Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T09:41:16.130Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T09:41:16.130Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T09:41:16.131Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T09:41:16.132Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T09:43:04.752Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T09:43:05.270Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-12T09:43:05.272Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T09:43:05.275Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T09:43:05.276Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T09:43:05.277Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T09:43:05.277Z"}
{"label":"sikka-server","level":"info","message":"SIGINT received, shutting down gracefully","service":"sikka-service","timestamp":"2025-09-12T09:43:55.307Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T09:45:05.396Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T09:45:05.665Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-12T09:45:05.667Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T09:45:05.671Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T09:45:05.671Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T09:45:05.672Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T09:45:05.672Z"}
{"label":"sikka-server","level":"info","message":"SIGINT received, shutting down gracefully","service":"sikka-service","timestamp":"2025-09-12T09:45:37.947Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T09:45:43.425Z"}
{"label":"sikka-server","level":"error","message":"Database connection error occurred","service":"sikka-service","timestamp":"2025-09-12T09:45:43.554Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T09:46:00.201Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T09:46:00.457Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-12T09:46:00.460Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T09:46:00.464Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T09:46:00.464Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T09:46:00.465Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T09:46:00.466Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T09:46:36.777Z"}
{"label":"sikka-server","level":"error","message":"Database connection error occurred","service":"sikka-service","timestamp":"2025-09-12T09:46:37.260Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T09:50:06.182Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T09:50:06.728Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-12T09:50:06.953Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T09:50:06.957Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T09:50:06.958Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T09:50:06.959Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T09:50:06.960Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T09:50:14.679Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T09:50:15.211Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-12T09:50:15.338Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T09:50:15.342Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T09:50:15.343Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T09:50:15.344Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T09:50:15.345Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T09:53:54.804Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T09:53:55.192Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T09:53:55.196Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T09:53:55.196Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T09:53:55.197Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T09:53:55.197Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T09:54:17.602Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T09:54:18.005Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T09:54:18.012Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T09:54:18.012Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T09:54:18.013Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T09:54:18.014Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T09:55:26.958Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T09:55:27.265Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T09:55:27.270Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T09:55:27.270Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T09:55:27.271Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T09:55:27.272Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T10:12:43.639Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T10:12:44.194Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T10:12:44.200Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T10:12:44.201Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T10:12:44.208Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T10:12:44.209Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T10:13:12.944Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-12T10:13:12.946Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T10:14:18.405Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T10:14:18.829Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T10:14:18.833Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T10:14:18.834Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T10:14:18.834Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T10:14:18.835Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T10:14:31.643Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T10:14:32.358Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T10:14:32.364Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T10:14:32.365Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T10:14:32.365Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T10:14:32.366Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T10:14:59.586Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T10:15:00.352Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T10:15:00.360Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T10:15:00.361Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T10:15:00.362Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T10:15:00.363Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T10:33:02.768Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-12T10:33:02.769Z"}
{"error":"connect ETIMEDOUT **************:443","label":"sikka-service","level":"error","message":"Request key API call failed","service":"sikka-service","timestamp":"2025-09-12T10:33:23.982Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T10:34:15.922Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T10:34:16.354Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T10:34:16.358Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T10:34:16.359Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T10:34:16.359Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T10:34:16.360Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T10:34:27.950Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-12T10:34:27.951Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T10:34:29.091Z"}
{"label":"sikka-controller","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-12T10:34:29.094Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T10:35:19.038Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T10:35:19.412Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T10:35:19.416Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T10:35:19.416Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T10:35:19.417Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T10:35:19.417Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T10:39:23.689Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-12T10:39:23.690Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T10:39:24.832Z"}
{"label":"sikka-controller","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-12T10:39:24.836Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T10:40:33.024Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T10:40:33.402Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T10:40:33.406Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T10:40:33.407Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T10:40:33.407Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T10:40:33.408Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T10:40:46.907Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T10:40:47.260Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T10:40:47.265Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T10:40:47.266Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T10:40:47.267Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T10:40:47.267Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T10:40:54.054Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T10:41:02.879Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T10:41:03.701Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T10:41:03.706Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T10:41:03.707Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T10:41:03.708Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T10:41:03.709Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T10:41:26.017Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T10:41:26.423Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T10:41:26.427Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T10:41:26.428Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T10:41:26.428Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T10:41:26.429Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T10:41:30.437Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-12T10:41:30.439Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T10:41:32.086Z"}
{"label":"sikka-controller","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-12T10:41:32.090Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T10:42:06.764Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T10:42:07.475Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T10:42:07.480Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T10:42:07.481Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T10:42:07.481Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T10:42:07.482Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T10:42:11.097Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-12T10:42:11.098Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T10:42:12.221Z"}
{"label":"sikka-controller","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-12T10:42:12.225Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T10:42:38.135Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T10:42:38.853Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T10:42:38.857Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T10:42:38.858Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T10:42:38.859Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T10:42:38.859Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T10:43:23.594Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T10:43:24.372Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T10:43:24.376Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T10:43:24.377Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T10:43:24.377Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T10:43:24.378Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T10:43:38.673Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T10:43:39.113Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T10:43:39.120Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T10:43:39.121Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T10:43:39.122Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T10:43:39.123Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T10:43:41.644Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-12T10:43:41.645Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T10:43:42.783Z"}
{"label":"sikka-controller","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-12T10:43:42.788Z"}
{"label":"sikka-service","level":"info","message":"Calling POST /request_key","service":"sikka-service","timestamp":"2025-09-12T10:43:42.785Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T10:43:43.122Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T10:44:09.811Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T10:44:11.135Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T10:44:11.148Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T10:44:11.151Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T10:44:11.154Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T10:44:11.156Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T10:44:12.796Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-12T10:44:12.799Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T10:44:14.038Z"}
{"label":"sikka-service","level":"info","message":"Calling POST /request_key","service":"sikka-service","timestamp":"2025-09-12T10:44:14.039Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T10:44:14.377Z"}
{"label":"sikka-controller","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-12T10:44:14.380Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T10:44:38.644Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T10:44:39.218Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T10:44:39.224Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T10:44:39.224Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T10:44:39.225Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T10:44:39.226Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T10:44:47.884Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T10:44:48.233Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T10:44:48.239Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T10:44:48.240Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T10:44:48.241Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T10:44:48.242Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T10:45:03.753Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T10:45:04.047Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T10:45:04.050Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T10:45:04.051Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T10:45:04.051Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T10:45:04.052Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T10:45:06.510Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-12T10:45:06.511Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T10:45:07.700Z"}
{"label":"sikka-service","level":"info","message":"Calling POST /request_key","service":"sikka-service","timestamp":"2025-09-12T10:45:07.701Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T10:45:08.070Z"}
{"label":"sikka-controller","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-12T10:45:08.071Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T10:45:42.509Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T10:45:43.001Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T10:45:43.007Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T10:45:43.007Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T10:45:43.008Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T10:45:43.009Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T10:45:45.326Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-12T10:45:45.328Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T10:45:46.447Z"}
{"label":"sikka-service","level":"info","message":"Calling POST /request_key","service":"sikka-service","timestamp":"2025-09-12T10:45:46.448Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T10:45:46.807Z"}
{"label":"sikka-controller","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-12T10:45:46.808Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T10:46:33.875Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T10:46:34.735Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T10:46:34.741Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T10:46:34.742Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T10:46:34.743Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T10:46:34.744Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T10:47:17.292Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T10:47:17.657Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T10:47:17.661Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T10:47:17.662Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T10:47:17.662Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T10:47:17.663Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T10:47:20.440Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-12T10:47:20.441Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T10:47:21.487Z"}
{"label":"sikka-service","level":"info","message":"Calling POST /request_key","service":"sikka-service","timestamp":"2025-09-12T10:47:21.488Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T10:47:21.846Z"}
{"label":"sikka-controller","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-12T10:47:21.847Z"}
{"label":"sikka-server","level":"info","message":"SIGINT received, shutting down gracefully","service":"sikka-service","timestamp":"2025-09-12T10:48:13.087Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T10:49:47.786Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T10:49:48.152Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-12T10:49:48.357Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T10:49:48.361Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T10:49:48.362Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T10:49:48.363Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T10:49:48.364Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T10:51:11.299Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T10:51:11.657Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T10:51:11.661Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T10:51:11.662Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T10:51:11.663Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T10:51:11.664Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T10:51:23.733Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-12T10:51:23.735Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T10:51:24.861Z"}
{"label":"sikka-service","level":"info","message":"Calling POST /request_key","service":"sikka-service","timestamp":"2025-09-12T10:51:24.863Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T10:51:25.214Z"}
{"label":"sikka-controller","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-12T10:51:25.215Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T10:52:32.332Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T10:52:32.700Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T10:52:32.705Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T10:52:32.705Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T10:52:32.706Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T10:52:32.707Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T10:52:48.868Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T10:52:50.233Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T10:52:50.240Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T10:52:50.242Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T10:52:50.243Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T10:52:50.244Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T10:52:53.356Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-12T10:52:53.357Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T10:52:54.629Z"}
{"label":"sikka-service","level":"info","message":"Calling POST /request_key","service":"sikka-service","timestamp":"2025-09-12T10:52:54.631Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T10:52:54.988Z"}
{"label":"sikka-controller","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-12T10:52:54.989Z"}
{"level":"info","message":"sikka_request_key created successfully with ID: 3908e906-ba2d-4f7e-8f75-97a0d047a32e","service":"sikka-service","timestamp":"2025-09-12T10:52:55.039Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:01:19.103Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:01:19.466Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:01:19.472Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:01:19.472Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:01:19.473Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:01:19.474Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T11:01:22.340Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-12T11:01:22.342Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T11:01:23.460Z"}
{"label":"sikka-service","level":"info","message":"Calling POST /request_key","service":"sikka-service","timestamp":"2025-09-12T11:01:23.461Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T11:01:23.810Z"}
{"label":"sikka-controller","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-12T11:01:23.811Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:01:50.270Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:01:50.901Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:01:50.907Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:01:50.907Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:01:50.908Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:01:50.909Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T11:02:08.988Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-12T11:02:08.989Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T11:02:10.035Z"}
{"label":"sikka-service","level":"info","message":"Calling POST /request_key","service":"sikka-service","timestamp":"2025-09-12T11:02:10.036Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T11:02:10.386Z"}
{"label":"sikka-controller","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-12T11:02:10.387Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:02:39.196Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:02:40.128Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:02:40.132Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:02:40.133Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:02:40.134Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:02:40.135Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:02:53.563Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:02:54.028Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:02:54.034Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:02:54.035Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:02:54.036Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:02:54.038Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:03:10.804Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:03:11.681Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:03:11.688Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:03:11.692Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:03:11.692Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:03:11.694Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:03:51.535Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:03:51.917Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:03:51.922Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:03:51.922Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:03:51.923Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:03:51.924Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:04:07.173Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:04:07.717Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:04:07.720Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:04:07.721Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:04:07.722Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:04:07.723Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T11:04:14.682Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-12T11:04:14.683Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T11:04:15.692Z"}
{"label":"sikka-service","level":"info","message":"Calling POST /request_key","service":"sikka-service","timestamp":"2025-09-12T11:04:15.693Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T11:04:16.113Z"}
{"label":"sikka-controller","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-12T11:04:16.114Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:05:14.364Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:05:14.732Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:05:14.737Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:05:14.737Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:05:14.738Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:05:14.739Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T11:05:21.154Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-12T11:05:21.155Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T11:05:22.238Z"}
{"label":"sikka-service","level":"info","message":"Calling POST /request_key","service":"sikka-service","timestamp":"2025-09-12T11:05:22.239Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T11:05:22.590Z"}
{"label":"sikka-controller","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-12T11:05:22.590Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:05:40.418Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:05:41.205Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:05:41.210Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:05:41.210Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:05:41.211Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:05:41.212Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:06:19.189Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:06:19.564Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:06:19.569Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:06:19.569Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:06:19.570Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:06:19.570Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T11:06:21.306Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-12T11:06:21.307Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T11:06:22.464Z"}
{"label":"sikka-service","level":"info","message":"Calling POST /request_key","service":"sikka-service","timestamp":"2025-09-12T11:06:22.466Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T11:06:25.728Z"}
{"label":"sikka-controller","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-12T11:06:25.729Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:06:42.564Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:06:43.390Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:06:43.398Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:06:43.398Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:06:43.399Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:06:43.401Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:06:51.611Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:06:51.947Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:06:51.952Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:06:51.953Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:06:51.953Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:06:51.954Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:07:00.659Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:07:01.537Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:07:01.542Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:07:01.543Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:07:01.543Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:07:01.544Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T11:07:04.160Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-12T11:07:04.162Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T11:07:05.486Z"}
{"label":"sikka-service","level":"info","message":"Calling POST /request_key","service":"sikka-service","timestamp":"2025-09-12T11:07:05.488Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T11:07:05.853Z"}
{"label":"sikka-controller","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-12T11:07:05.854Z"}
{"level":"info","message":"sikka_request_key updated successfully with ID: 3908e906-ba2d-4f7e-8f75-97a0d047a32e","service":"sikka-service","timestamp":"2025-09-12T11:07:05.919Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T11:07:22.847Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-12T11:07:22.848Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T11:07:23.917Z"}
{"label":"sikka-service","level":"info","message":"Calling POST /request_key","service":"sikka-service","timestamp":"2025-09-12T11:07:23.921Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T11:07:24.305Z"}
{"label":"sikka-controller","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-12T11:07:24.306Z"}
{"level":"info","message":"sikka_request_key updated successfully with ID: 3908e906-ba2d-4f7e-8f75-97a0d047a32e","service":"sikka-service","timestamp":"2025-09-12T11:07:24.541Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:07:58.835Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:08:45.760Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:08:46.221Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:08:46.225Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:08:46.225Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:08:46.226Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:08:46.227Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T11:08:48.432Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-12T11:08:48.433Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T11:08:49.475Z"}
{"label":"sikka-service","level":"info","message":"Calling POST /request_key","service":"sikka-service","timestamp":"2025-09-12T11:08:49.476Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T11:08:49.818Z"}
{"label":"sikka-controller","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-12T11:08:49.819Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:09:06.112Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:09:06.982Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:09:06.987Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:09:06.988Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:09:06.989Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:09:06.990Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T11:09:11.004Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-12T11:09:11.006Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T11:09:12.082Z"}
{"label":"sikka-service","level":"info","message":"Calling POST /request_key","service":"sikka-service","timestamp":"2025-09-12T11:09:12.084Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T11:09:12.519Z"}
{"label":"sikka-controller","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-12T11:09:12.520Z"}
{"level":"info","message":"sikka_request_key updated successfully with ID: 3908e906-ba2d-4f7e-8f75-97a0d047a32e","service":"sikka-service","timestamp":"2025-09-12T11:09:12.564Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:10:08.256Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:10:08.782Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:10:08.792Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:10:08.793Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:10:08.793Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:10:08.794Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:10:28.110Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:10:28.820Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:10:28.826Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:10:28.827Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:10:28.827Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:10:28.829Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T11:10:32.988Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-12T11:10:32.990Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T11:10:34.262Z"}
{"label":"sikka-service","level":"info","message":"Calling POST /request_key","service":"sikka-service","timestamp":"2025-09-12T11:10:34.264Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T11:10:35.793Z"}
{"label":"sikka-controller","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-12T11:10:35.794Z"}
{"level":"info","message":"sikka_request_key updated successfully with ID: 3908e906-ba2d-4f7e-8f75-97a0d047a32e","service":"sikka-service","timestamp":"2025-09-12T11:10:35.862Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:11:23.179Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:11:24.061Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:11:24.065Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:11:24.066Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:11:24.067Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:11:24.068Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:12:06.367Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:12:06.749Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:12:06.755Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:12:06.756Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:12:06.757Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:12:06.758Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:12:15.522Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:12:16.501Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:12:16.506Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:12:16.507Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:12:16.509Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:12:16.510Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T11:12:31.185Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-12T11:12:31.187Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T11:12:32.317Z"}
{"label":"sikka-service","level":"info","message":"Calling POST /request_key","service":"sikka-service","timestamp":"2025-09-12T11:12:32.318Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T11:12:32.672Z"}
{"label":"sikka-controller","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-12T11:12:32.673Z"}
{"level":"info","message":"sikka_request_key updated successfully with ID: 3908e906-ba2d-4f7e-8f75-97a0d047a32e","service":"sikka-service","timestamp":"2025-09-12T11:12:32.840Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:13:20.304Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:13:20.661Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:13:20.667Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:13:20.667Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:13:20.668Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:13:20.669Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T11:13:25.517Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-12T11:13:25.518Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T11:13:26.643Z"}
{"label":"sikka-service","level":"info","message":"Calling POST /request_key","service":"sikka-service","timestamp":"2025-09-12T11:13:26.645Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T11:13:27.038Z"}
{"label":"sikka-controller","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-12T11:13:27.038Z"}
{"level":"info","message":"sikka_request_key updated successfully with ID: 3908e906-ba2d-4f7e-8f75-97a0d047a32e","service":"sikka-service","timestamp":"2025-09-12T11:13:27.082Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:14:36.235Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:14:36.905Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:14:36.910Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:14:36.910Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:14:36.911Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:14:36.912Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T11:14:58.390Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-12T11:14:58.392Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T11:15:00.530Z"}
{"label":"sikka-service","level":"info","message":"Calling POST /request_key","service":"sikka-service","timestamp":"2025-09-12T11:15:00.532Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T11:15:00.967Z"}
{"label":"sikka-controller","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-12T11:15:00.968Z"}
{"level":"info","message":"sikka_request_key updated successfully with ID: 3908e906-ba2d-4f7e-8f75-97a0d047a32e","service":"sikka-service","timestamp":"2025-09-12T11:15:01.179Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:16:48.154Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:16:48.504Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:16:48.508Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:16:48.509Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:16:48.509Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:16:48.510Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:17:01.958Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:17:02.646Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:17:02.650Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:17:02.651Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:17:02.652Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:17:02.652Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T11:17:05.400Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-12T11:17:05.401Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T11:17:06.576Z"}
{"label":"sikka-service","level":"info","message":"Calling POST /request_key","service":"sikka-service","timestamp":"2025-09-12T11:17:06.577Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T11:17:06.949Z"}
{"label":"sikka-controller","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-12T11:17:06.950Z"}
{"level":"info","message":"sikka_request_key created successfully with ID: 67bf6ea6-a17b-49af-89aa-0092432297e9","service":"sikka-service","timestamp":"2025-09-12T11:17:07.002Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:17:45.089Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:17:45.832Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:17:45.838Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:17:45.839Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:17:45.839Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:17:45.840Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:17:55.526Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:17:55.946Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:17:55.950Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:17:55.951Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:17:55.952Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:17:55.953Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T11:18:00.681Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-12T11:18:00.683Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T11:18:01.952Z"}
{"label":"sikka-service","level":"info","message":"Calling POST /request_key","service":"sikka-service","timestamp":"2025-09-12T11:18:01.956Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T11:18:02.302Z"}
{"label":"sikka-controller","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-12T11:18:02.303Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:18:38.634Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:18:39.341Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:18:39.348Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:18:39.348Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:18:39.350Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:18:39.351Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T11:18:41.247Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-12T11:18:41.249Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T11:18:42.334Z"}
{"label":"sikka-service","level":"info","message":"Calling POST /request_key","service":"sikka-service","timestamp":"2025-09-12T11:18:42.335Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T11:18:42.687Z"}
{"label":"sikka-controller","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-12T11:18:42.688Z"}
{"level":"info","message":"sikka_request_key created successfully with ID: a90af7ba-5b2a-41da-9d3a-b4ad7862fd28","service":"sikka-service","timestamp":"2025-09-12T11:18:42.737Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T11:18:54.719Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-12T11:18:54.720Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T11:18:55.737Z"}
{"label":"sikka-service","level":"info","message":"Calling POST /request_key","service":"sikka-service","timestamp":"2025-09-12T11:18:55.738Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T11:18:56.096Z"}
{"label":"sikka-controller","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-12T11:18:56.096Z"}
{"level":"info","message":"sikka_request_key updated successfully with ID: a90af7ba-5b2a-41da-9d3a-b4ad7862fd28","service":"sikka-service","timestamp":"2025-09-12T11:18:56.253Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:23:08.587Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:23:09.018Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:23:09.025Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:23:09.026Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:23:09.026Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:23:09.027Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:24:33.649Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:24:34.022Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:24:34.027Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:24:34.028Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:24:34.029Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:24:34.030Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:25:59.877Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:26:00.475Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:26:00.481Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:26:00.482Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:26:00.482Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:26:00.483Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:26:12.213Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:26:12.623Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:26:12.628Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:26:12.629Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:26:12.630Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:26:12.631Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:26:34.844Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:26:35.247Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:26:35.251Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:26:35.252Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:26:35.253Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:26:35.254Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:26:45.106Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:26:45.479Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:26:45.485Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:26:45.485Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:26:45.486Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:26:45.487Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T11:28:10.610Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:28:35.390Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:28:35.759Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:28:35.763Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:28:35.763Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:28:35.764Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:28:35.765Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T11:28:40.662Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:29:20.425Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:29:20.787Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:29:20.792Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:29:20.793Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:29:20.793Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:29:20.794Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T11:29:22.872Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:30:21.310Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:30:21.756Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:30:21.763Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:30:21.764Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:30:21.764Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:30:21.765Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:30:42.048Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:30:42.356Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:30:42.360Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:30:42.361Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:30:42.362Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:30:42.362Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T11:30:45.184Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:32:15.002Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:32:15.478Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:32:15.482Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:32:15.483Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:32:15.483Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:32:15.484Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:32:50.595Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:32:51.027Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:32:51.032Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:32:51.033Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:32:51.034Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:32:51.035Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T11:33:03.898Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:33:17.199Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:33:17.639Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:33:17.643Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:33:17.644Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:33:17.645Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:33:17.646Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T11:33:22.542Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:36:22.693Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:36:23.038Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:36:23.042Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:36:23.042Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:36:23.043Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:36:23.043Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T11:36:25.590Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:37:10.041Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:37:10.486Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:37:10.490Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:37:10.491Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:37:10.492Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:37:10.493Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:37:45.384Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:37:45.867Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:37:45.871Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:37:45.872Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:37:45.873Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:37:45.873Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T11:37:47.849Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:38:27.042Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:38:27.429Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:38:27.433Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:38:27.435Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:38:27.436Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:38:27.437Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:38:55.023Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:38:55.338Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:38:55.342Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:38:55.342Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:38:55.343Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:38:55.343Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:40:42.604Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:40:42.994Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:40:42.999Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:40:42.999Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:40:43.000Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:40:43.002Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:42:13.388Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:42:13.772Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:42:13.776Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:42:13.777Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:42:13.778Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:42:13.778Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:43:33.044Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:43:33.628Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:43:33.632Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:43:33.633Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:43:33.633Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:43:33.634Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:43:51.815Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:43:52.169Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:43:52.173Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:43:52.174Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:43:52.174Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:43:52.175Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:44:17.578Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:44:18.005Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:44:18.009Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:44:18.010Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:44:18.010Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:44:18.011Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:44:51.796Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:44:52.334Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:44:52.338Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:44:52.339Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:44:52.339Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:44:52.340Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:45:30.811Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:45:31.364Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:45:31.370Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:45:31.370Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:45:31.372Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:45:31.373Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:45:59.910Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:46:00.298Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:46:00.302Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:46:00.303Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:46:00.303Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:46:00.304Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:46:14.223Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:46:14.650Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:46:14.654Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:46:14.654Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:46:14.655Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:46:14.655Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T11:46:28.749Z"}
{"label":"sikka-service","level":"info","message":"Calling POST /account_receivables?practice_id=1","service":"sikka-service","timestamp":"2025-09-12T11:46:28.905Z"}
{"error":"Request failed with status code 405","label":"sikka-service","level":"error","message":"Request key API call failed","service":"sikka-service","timestamp":"2025-09-12T11:46:30.011Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:46:58.452Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:46:58.757Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:46:58.762Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:46:58.762Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:46:58.763Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:46:58.764Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T11:47:05.265Z"}
{"label":"sikka-service","level":"info","message":"Calling POST /account_receivables?practice_id=1","service":"sikka-service","timestamp":"2025-09-12T11:47:05.295Z"}
{"error":"Request failed with status code 405","label":"sikka-service","level":"error","message":"Request key API call failed","service":"sikka-service","timestamp":"2025-09-12T11:47:08.920Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:47:32.631Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:47:32.964Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:47:32.968Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:47:32.969Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:47:32.969Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:47:32.970Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T11:47:35.968Z"}
{"label":"sikka-service","level":"info","message":"Calling POST /account_receivables?practice_id=1","service":"sikka-service","timestamp":"2025-09-12T11:47:35.995Z"}
{"error":"Request failed with status code 405","label":"sikka-service","level":"error","message":"Request key API call failed","service":"sikka-service","timestamp":"2025-09-12T11:47:37.738Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:48:32.414Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:48:33.036Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:48:33.040Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:48:33.040Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:48:33.041Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:48:33.042Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T11:48:45.619Z"}
{"label":"sikka-service","level":"info","message":"Calling GET /account_receivables?practice_id=1","service":"sikka-service","timestamp":"2025-09-12T11:48:45.771Z"}
{"error":"Request failed with status code 401","label":"sikka-service","level":"error","message":"Request key API call failed","service":"sikka-service","timestamp":"2025-09-12T11:48:47.091Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:49:52.621Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:49:53.079Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:49:53.085Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:49:53.086Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:49:53.087Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:49:53.088Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T11:50:00.690Z"}
{"label":"sikka-service","level":"info","message":"Calling GET /accounts_receivables?practice_id=1","service":"sikka-service","timestamp":"2025-09-12T11:50:00.717Z"}
{"label":"sikka-service","level":"info","message":"Account receivables fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T11:50:02.143Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:52:05.111Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:52:05.481Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:52:05.484Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:52:05.485Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:52:05.486Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:52:05.486Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:53:25.523Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:53:25.975Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-12T11:53:26.194Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:53:26.201Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:53:26.202Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:53:26.203Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:53:26.203Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:54:53.554Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:54:54.099Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-12T11:54:54.500Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:54:54.507Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:54:54.508Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:54:54.509Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:54:54.510Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:55:31.885Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:55:32.244Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-12T11:55:32.363Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:55:32.369Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:55:32.370Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:55:32.371Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:55:32.372Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:55:57.919Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:55:58.579Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-12T11:55:58.825Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:55:58.829Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:55:58.830Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:55:58.831Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:55:58.832Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:56:36.908Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:56:37.554Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-12T11:56:37.810Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:56:37.814Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:56:37.815Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:56:37.815Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:56:37.816Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:56:57.780Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:58:40.983Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:58:41.326Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-12T11:58:41.425Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:58:41.429Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:58:41.429Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:58:41.430Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:58:41.430Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T11:59:00.132Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T11:59:00.452Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-12T11:59:00.550Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T11:59:00.554Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T11:59:00.555Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T11:59:00.556Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T11:59:00.556Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T12:00:06.294Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T12:00:06.701Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-12T12:00:06.816Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T12:00:06.821Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T12:00:06.822Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T12:00:06.823Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T12:00:06.824Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T12:01:18.596Z"}
{"label":"sikka-service","level":"info","message":"Calling GET /accounts_receivables?practice_id=1","service":"sikka-service","timestamp":"2025-09-12T12:01:18.792Z"}
{"label":"sikka-service","level":"info","message":"Account receivables fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T12:01:19.946Z"}
{"label":"sikka-server","level":"info","message":"SIGINT received, shutting down gracefully","service":"sikka-service","timestamp":"2025-09-12T12:01:40.598Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T12:01:48.078Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T12:01:48.388Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-12T12:01:48.492Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T12:01:48.496Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T12:01:48.497Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T12:01:48.497Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T12:01:48.498Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T12:02:06.590Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T12:02:06.927Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-12T12:02:07.019Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T12:02:07.023Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T12:02:07.023Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T12:02:07.024Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T12:02:07.025Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T12:02:09.757Z"}
{"label":"sikka-service","level":"info","message":"Calling GET /accounts_receivables?practice_id=1","service":"sikka-service","timestamp":"2025-09-12T12:02:09.794Z"}
{"label":"sikka-service","level":"info","message":"Account receivables fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T12:02:10.926Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T12:02:42.950Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T12:03:00.684Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T12:03:01.223Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-12T12:03:01.389Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T12:03:01.395Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T12:03:01.396Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T12:03:01.397Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T12:03:01.398Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T12:03:12.557Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T12:03:13.258Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-12T12:03:13.547Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T12:03:13.552Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T12:03:13.553Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T12:03:13.554Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T12:03:13.554Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T12:03:40.840Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T12:03:41.200Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-12T12:03:41.306Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T12:03:41.310Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T12:03:41.311Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T12:03:41.312Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T12:03:41.313Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T12:03:44.901Z"}
{"label":"sikka-service","level":"info","message":"Calling GET /accounts_receivables?practice_id=1","service":"sikka-service","timestamp":"2025-09-12T12:03:44.941Z"}
{"label":"sikka-service","level":"info","message":"Account receivables fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T12:03:46.420Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T12:04:34.647Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T12:04:35.066Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-12T12:04:35.181Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T12:04:35.186Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T12:04:35.187Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T12:04:35.188Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T12:04:35.189Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T12:04:39.217Z"}
{"label":"sikka-service","level":"info","message":"Calling GET /accounts_receivables?practice_id=1","service":"sikka-service","timestamp":"2025-09-12T12:04:39.247Z"}
{"label":"sikka-service","level":"info","message":"Account receivables fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T12:04:40.349Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T12:05:09.926Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T12:05:10.987Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-12T12:05:11.242Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T12:05:11.246Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T12:05:11.246Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T12:05:11.247Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T12:05:11.248Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T12:05:13.977Z"}
{"label":"sikka-service","level":"info","message":"Calling GET /accounts_receivables?practice_id=1","service":"sikka-service","timestamp":"2025-09-12T12:05:13.998Z"}
{"label":"sikka-service","level":"info","message":"Account receivables fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T12:05:15.125Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 17789de1-d394-42e3-9af2-42dbefacbf32","service":"sikka-service","timestamp":"2025-09-12T12:05:15.176Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T12:08:31.432Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T12:08:31.773Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-12T12:08:31.871Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T12:08:31.875Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T12:08:31.875Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T12:08:31.876Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T12:08:31.877Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T12:09:52.174Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T12:09:52.792Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-12T12:09:52.974Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T12:09:52.978Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T12:09:52.978Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T12:09:52.979Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T12:09:52.980Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T12:09:58.774Z"}
{"label":"sikka-service","level":"info","message":"Calling GET /accounts_receivables?practice_id=1","service":"sikka-service","timestamp":"2025-09-12T12:09:58.802Z"}
{"label":"sikka-service","level":"info","message":"Account receivables fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T12:10:00.089Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T12:10:42.995Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T12:10:43.582Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-12T12:10:43.829Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T12:10:43.834Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T12:10:43.835Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T12:10:43.836Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T12:10:43.837Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T12:10:45.883Z"}
{"label":"sikka-service","level":"info","message":"Calling GET /accounts_receivables?practice_id=1","service":"sikka-service","timestamp":"2025-09-12T12:10:45.911Z"}
{"label":"sikka-service","level":"info","message":"Account receivables fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T12:10:47.142Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T12:11:26.432Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T12:11:26.849Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-12T12:11:26.963Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T12:11:26.968Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T12:11:26.969Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T12:11:26.970Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T12:11:26.970Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T12:11:29.110Z"}
{"label":"sikka-service","level":"info","message":"Calling GET /accounts_receivables?practice_id=1","service":"sikka-service","timestamp":"2025-09-12T12:11:29.137Z"}
{"label":"sikka-service","level":"info","message":"Account receivables fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T12:11:30.230Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T12:12:08.881Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T12:12:09.689Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-12T12:12:09.955Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T12:12:09.960Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T12:12:09.960Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T12:12:09.961Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T12:12:09.962Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-12T12:12:12.483Z"}
{"label":"sikka-service","level":"info","message":"Calling GET /accounts_receivables?practice_id=1","service":"sikka-service","timestamp":"2025-09-12T12:12:12.511Z"}
{"label":"sikka-service","level":"info","message":"Account receivables fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-12T12:12:13.835Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 4826cbb2-a181-4b37-97ec-410044e9b443","service":"sikka-service","timestamp":"2025-09-12T12:12:13.891Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 9edab9be-c688-4ba5-b001-e26a4fadca43","service":"sikka-service","timestamp":"2025-09-12T12:12:13.895Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: e24d7413-bc3f-47f8-afc9-098706a3e314","service":"sikka-service","timestamp":"2025-09-12T12:12:13.897Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: cf53326d-6e13-4245-8d5d-9ab91c54a972","service":"sikka-service","timestamp":"2025-09-12T12:12:13.899Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 6d35997d-ecf4-4ddc-b9a6-1551877cd890","service":"sikka-service","timestamp":"2025-09-12T12:12:13.901Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 71e7c50c-1c5c-4637-abba-2ea40309a9bd","service":"sikka-service","timestamp":"2025-09-12T12:12:13.903Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 9403fde7-d18b-4f18-ab20-fc7e9c729ec0","service":"sikka-service","timestamp":"2025-09-12T12:12:13.905Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: cbe3e26b-09dd-4762-8eed-36d57a5a6327","service":"sikka-service","timestamp":"2025-09-12T12:12:13.907Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 0b640b9b-1cf7-4112-a367-463abbd2022e","service":"sikka-service","timestamp":"2025-09-12T12:12:13.911Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: d5e1e32e-c067-4985-abd0-bd795c797a4b","service":"sikka-service","timestamp":"2025-09-12T12:12:13.913Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 0ffb3de5-4e31-43c4-a909-ecb2c8fbb4db","service":"sikka-service","timestamp":"2025-09-12T12:12:13.915Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 9083e98e-7bea-499a-b70d-6bfde4f48c8d","service":"sikka-service","timestamp":"2025-09-12T12:12:13.917Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 86821cb7-0a34-4659-96ba-62edeae9f4d2","service":"sikka-service","timestamp":"2025-09-12T12:12:13.919Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: b3493e3b-4d88-45d9-b76a-c671c1b4724b","service":"sikka-service","timestamp":"2025-09-12T12:12:13.921Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 0d6d27b8-e86f-4b0c-b4bc-2f10e137a796","service":"sikka-service","timestamp":"2025-09-12T12:12:13.923Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: a38c4030-4bab-4993-95ff-b64129190d63","service":"sikka-service","timestamp":"2025-09-12T12:12:13.926Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: db69b89a-df4b-4061-a905-209fa63edbc3","service":"sikka-service","timestamp":"2025-09-12T12:12:13.928Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: dc735d07-f916-4e20-9526-************","service":"sikka-service","timestamp":"2025-09-12T12:12:13.930Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: c8b38591-1523-4013-9a20-0f06d565f784","service":"sikka-service","timestamp":"2025-09-12T12:12:13.932Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 17925d13-4254-4c69-9e2d-334e4e09bc01","service":"sikka-service","timestamp":"2025-09-12T12:12:13.934Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 515c9fe5-423a-43dd-91a2-bb3005b433ca","service":"sikka-service","timestamp":"2025-09-12T12:12:13.936Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 49c30f67-26b8-4f91-9f46-4adbb0ad063e","service":"sikka-service","timestamp":"2025-09-12T12:12:13.938Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 894fc8e8-5a61-4192-ac4b-f09ceecb7fa7","service":"sikka-service","timestamp":"2025-09-12T12:12:13.939Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 70ab5de1-4688-4ea1-a8e9-a3ab728af54f","service":"sikka-service","timestamp":"2025-09-12T12:12:13.941Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: a388087f-bbb2-4f1c-9e04-cbab56408836","service":"sikka-service","timestamp":"2025-09-12T12:12:13.943Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 4d15c16f-33d5-4e86-b5c7-61716818f2e1","service":"sikka-service","timestamp":"2025-09-12T12:12:13.945Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 1d3ad040-f1da-43c4-a9ba-9c4edf2882a4","service":"sikka-service","timestamp":"2025-09-12T12:12:13.952Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 086af497-2d8b-4798-bd10-8b9c62f97206","service":"sikka-service","timestamp":"2025-09-12T12:12:13.954Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 8649584f-6489-4006-b0d6-023b9d825fa1","service":"sikka-service","timestamp":"2025-09-12T12:12:13.955Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 11c93fd3-a2c6-488c-b4f5-4ecb42631114","service":"sikka-service","timestamp":"2025-09-12T12:12:13.957Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: a6583535-b879-4ca9-b1c5-0056cf82ce91","service":"sikka-service","timestamp":"2025-09-12T12:12:13.959Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 926f535e-aa42-4d92-b972-4a0296cd8346","service":"sikka-service","timestamp":"2025-09-12T12:12:13.962Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 7e385b5a-6e2b-45da-9e26-821777343ba3","service":"sikka-service","timestamp":"2025-09-12T12:12:13.963Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 5f6b6dd2-6d86-4bdd-8c11-4c9f5d8ac557","service":"sikka-service","timestamp":"2025-09-12T12:12:13.966Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 00e74c2e-89af-42e4-a6df-cc86b7a373b3","service":"sikka-service","timestamp":"2025-09-12T12:12:13.968Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 64e4818d-4a14-4609-ae51-a83a31a6b0c9","service":"sikka-service","timestamp":"2025-09-12T12:12:13.970Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 3ff9803c-aee5-45b2-814a-8d4a0b11e123","service":"sikka-service","timestamp":"2025-09-12T12:12:13.972Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: b34a933e-a7e3-4994-bc0d-354557eacef2","service":"sikka-service","timestamp":"2025-09-12T12:12:13.974Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 6b3a714a-a5f2-4d54-897e-5bc1fc2fe97e","service":"sikka-service","timestamp":"2025-09-12T12:12:13.976Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: cddb5080-8008-490e-8024-844fa98944fe","service":"sikka-service","timestamp":"2025-09-12T12:12:13.978Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: c83dfc3e-7240-4764-af56-e87cbf2e2ceb","service":"sikka-service","timestamp":"2025-09-12T12:12:13.981Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 0e004c1e-fa6c-4e1c-9ef3-4c4d1dbb5237","service":"sikka-service","timestamp":"2025-09-12T12:12:13.984Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 7ed4bf8b-feaa-4a18-b728-ecf7e54961e0","service":"sikka-service","timestamp":"2025-09-12T12:12:13.985Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 6ded1d94-bdcc-44b6-adc4-9e0661822b1d","service":"sikka-service","timestamp":"2025-09-12T12:12:13.987Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: b5779f7f-9d8d-451b-8547-5bf5fa2b6b56","service":"sikka-service","timestamp":"2025-09-12T12:12:13.990Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: fd829d8e-430f-4d01-a800-8eeef1f43668","service":"sikka-service","timestamp":"2025-09-12T12:12:13.992Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: bfc415ff-a740-441e-991f-7498a03c640a","service":"sikka-service","timestamp":"2025-09-12T12:12:13.994Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: a5a8356e-ef77-43b1-80b0-2ec30b494bbf","service":"sikka-service","timestamp":"2025-09-12T12:12:13.996Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 5f87730e-11e8-4215-ac80-41f455b1fe5f","service":"sikka-service","timestamp":"2025-09-12T12:12:13.998Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 9a01e782-4784-4004-b888-5c22faf71853","service":"sikka-service","timestamp":"2025-09-12T12:12:14.000Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T12:16:43.809Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T12:16:44.191Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-12T12:16:44.301Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T12:16:44.309Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T12:16:44.310Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T12:16:44.311Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T12:16:44.311Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-12T12:17:42.409Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-12T12:17:43.109Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-12T12:17:43.369Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-12T12:17:43.374Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-12T12:17:43.376Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-12T12:17:43.377Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-12T12:17:43.378Z"}
{"label":"sikka-server","level":"info","message":"SIGINT received, shutting down gracefully","service":"sikka-service","timestamp":"2025-09-12T12:18:14.177Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-13T05:46:35.200Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-13T05:46:35.565Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-13T05:46:35.675Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-13T05:46:35.679Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-13T05:46:35.679Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-13T05:46:35.680Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-13T05:46:35.680Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-13T05:47:18.789Z"}
{"label":"sikka-service","level":"info","message":"Calling GET /accounts_receivables?practice_id=1","service":"sikka-service","timestamp":"2025-09-13T05:47:18.942Z"}
{"label":"sikka-service","level":"info","message":"Account receivables fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-13T05:47:20.747Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 32d6856f-1590-4ef8-8645-498f017adc07","service":"sikka-service","timestamp":"2025-09-13T05:47:20.825Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 1a183cbf-dd2b-46ea-a07f-54a8fea7cc2e","service":"sikka-service","timestamp":"2025-09-13T05:47:20.827Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 9f8cb82b-4ee9-43a4-96f9-bf4be78d3164","service":"sikka-service","timestamp":"2025-09-13T05:47:20.834Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 0edab8ec-8c46-4c73-91d8-510dd5862c49","service":"sikka-service","timestamp":"2025-09-13T05:47:20.837Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: ad9b0c19-5f6f-479d-a789-05cceb02eafa","service":"sikka-service","timestamp":"2025-09-13T05:47:20.839Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: f9d1c4c5-a80a-4349-af44-0a78920bc763","service":"sikka-service","timestamp":"2025-09-13T05:47:20.841Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: cddb46b3-bee9-4191-8ccf-c371f82f2ff2","service":"sikka-service","timestamp":"2025-09-13T05:47:20.843Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 57d1beef-5b23-44aa-aa5e-4b9b7ba96459","service":"sikka-service","timestamp":"2025-09-13T05:47:20.845Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 7261f7be-6368-4b97-a67b-0f0d48127569","service":"sikka-service","timestamp":"2025-09-13T05:47:20.847Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 726cca8c-b0e0-402d-9c95-d2a10dab680f","service":"sikka-service","timestamp":"2025-09-13T05:47:20.849Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 4a938ef3-2947-4245-92af-15da6b0296bd","service":"sikka-service","timestamp":"2025-09-13T05:47:20.851Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 523268bf-2300-44b5-8fc5-34ef8f1baafc","service":"sikka-service","timestamp":"2025-09-13T05:47:20.853Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 4b15d0ba-55c0-41ce-bc63-341513ed28ae","service":"sikka-service","timestamp":"2025-09-13T05:47:20.855Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: a1fbc06d-986b-46fd-8403-cad0e44a56f9","service":"sikka-service","timestamp":"2025-09-13T05:47:20.858Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 1e392d59-946a-41e3-9ea7-5e364c624ace","service":"sikka-service","timestamp":"2025-09-13T05:47:20.860Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: b5be7f79-2987-4fc3-9758-ccdcbdd3aca8","service":"sikka-service","timestamp":"2025-09-13T05:47:20.863Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 3dadbaee-28fd-414e-874d-3c9193301faa","service":"sikka-service","timestamp":"2025-09-13T05:47:20.865Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 5f0bbd26-851b-4ad0-a6bf-8987233fb0d9","service":"sikka-service","timestamp":"2025-09-13T05:47:20.867Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: d32c5703-348e-4b17-9055-bb2412b5aab2","service":"sikka-service","timestamp":"2025-09-13T05:47:20.869Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: afcab4a8-1fc1-40cd-8847-e90dfdd617e5","service":"sikka-service","timestamp":"2025-09-13T05:47:20.872Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 955ff111-62ab-45d8-8323-8ec45b50914e","service":"sikka-service","timestamp":"2025-09-13T05:47:20.874Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 3ee27f62-277b-41c6-b6fe-6a14fd3da6bf","service":"sikka-service","timestamp":"2025-09-13T05:47:20.876Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 7c050bec-cbdb-488f-a6de-60b80d81c074","service":"sikka-service","timestamp":"2025-09-13T05:47:20.878Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: f8ecd1e1-3e29-4c25-871a-a1f32cbc2c96","service":"sikka-service","timestamp":"2025-09-13T05:47:20.880Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 620a93cf-01ac-4eb1-87e5-09cbf8f89d9e","service":"sikka-service","timestamp":"2025-09-13T05:47:20.883Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: b47c2bd3-c296-4810-a445-90feb6d0b458","service":"sikka-service","timestamp":"2025-09-13T05:47:20.885Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: b878b5bc-18fd-497e-8de0-6b03aa8258d9","service":"sikka-service","timestamp":"2025-09-13T05:47:20.887Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: fec7f974-a5be-4409-9afa-ba1a9bb6e0e7","service":"sikka-service","timestamp":"2025-09-13T05:47:20.889Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 7646472e-d492-4bd1-b6c8-bbbef5282516","service":"sikka-service","timestamp":"2025-09-13T05:47:20.891Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 5208de9f-4b78-476f-9f01-7b10feac834c","service":"sikka-service","timestamp":"2025-09-13T05:47:20.893Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 5f24ec8b-1d09-4c25-8a0a-c5e45ca52fe1","service":"sikka-service","timestamp":"2025-09-13T05:47:20.895Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: b8453e4e-0b3f-474d-9bc2-2ec7e19d32c9","service":"sikka-service","timestamp":"2025-09-13T05:47:20.897Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 4f765e15-5976-4ebf-9038-c7cadc7bf3fa","service":"sikka-service","timestamp":"2025-09-13T05:47:20.899Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 90f1be3c-7c9b-4105-a44b-46ef569a360b","service":"sikka-service","timestamp":"2025-09-13T05:47:20.900Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: ********-45c9-4053-871f-1d77df53b08f","service":"sikka-service","timestamp":"2025-09-13T05:47:20.902Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 880c533e-e112-4a3a-96d8-53707518781b","service":"sikka-service","timestamp":"2025-09-13T05:47:20.905Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: c8abe76d-a704-4173-8c72-a55ccb5e613a","service":"sikka-service","timestamp":"2025-09-13T05:47:20.907Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 14ac457d-cd82-4976-8879-38c1d92c19fe","service":"sikka-service","timestamp":"2025-09-13T05:47:20.909Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: a5b919e5-d248-4d7a-80f9-03eaf20d9ea0","service":"sikka-service","timestamp":"2025-09-13T05:47:20.911Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: cf8f86ae-e10b-40eb-97bc-0b751970e992","service":"sikka-service","timestamp":"2025-09-13T05:47:20.913Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 3f400e9b-3a69-445f-b452-f9fe243ba6a4","service":"sikka-service","timestamp":"2025-09-13T05:47:20.915Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 4f2f9949-a0a9-4d00-8466-22a30c1be0d0","service":"sikka-service","timestamp":"2025-09-13T05:47:20.918Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 2e207651-9029-4e9d-b8f0-76e674b5d865","service":"sikka-service","timestamp":"2025-09-13T05:47:20.920Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: b08255e8-ea15-4bf1-a503-b0c905d5608b","service":"sikka-service","timestamp":"2025-09-13T05:47:20.922Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 2045e8f4-aeff-4a70-babf-5ffa4519708a","service":"sikka-service","timestamp":"2025-09-13T05:47:20.924Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: ae05bd89-a623-4385-9f83-91b6f2dbb6d8","service":"sikka-service","timestamp":"2025-09-13T05:47:20.926Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 621ed88b-7b49-4338-bf61-3f947c444a15","service":"sikka-service","timestamp":"2025-09-13T05:47:20.928Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 27ef012e-f9c6-4548-ac2e-e199173c635e","service":"sikka-service","timestamp":"2025-09-13T05:47:20.930Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: f80351b8-bc03-4cc2-8893-4970a3d05cd5","service":"sikka-service","timestamp":"2025-09-13T05:47:20.932Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: a81ba2bc-cd5b-4bab-bc30-04ed5a9ba395","service":"sikka-service","timestamp":"2025-09-13T05:47:20.933Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-13T05:49:28.655Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-13T05:49:28.962Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-13T05:49:29.064Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-13T05:49:29.068Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-13T05:49:29.069Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-13T05:49:29.070Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-13T05:49:29.070Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-13T05:49:32.625Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-13T05:49:32.650Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-13T05:49:32.651Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-13T05:49:33.835Z"}
{"label":"sikka-service","level":"info","message":"Calling POST /request_key","service":"sikka-service","timestamp":"2025-09-13T05:49:33.836Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","service":"sikka-service","status":200,"timestamp":"2025-09-13T05:49:34.220Z"}
{"label":"sikka-controller","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-13T05:49:34.221Z"}
{"level":"info","message":"sikka_request_key updated successfully with ID: a90af7ba-5b2a-41da-9d3a-b4ad7862fd28","service":"sikka-service","timestamp":"2025-09-13T05:49:34.244Z"}
{"error":"Cannot read properties of undefined (reading 'request_key')","label":"sikka-controller","level":"error","message":"Failed to fetch account receivables","service":"sikka-service","timestamp":"2025-09-13T05:49:34.245Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-13T05:50:14.120Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-13T05:50:14.438Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-13T05:50:14.536Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-13T05:50:14.540Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-13T05:50:14.540Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-13T05:50:14.541Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-13T05:50:14.541Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-13T05:50:23.584Z"}
{"label":"sikka-service","level":"info","message":"Calling GET /accounts_receivables?practice_id=1","service":"sikka-service","timestamp":"2025-09-13T05:50:23.610Z"}
{"label":"sikka-service","level":"info","message":"Account receivables fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-13T05:50:24.860Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 1041ed6e-6aad-4708-95d7-944182182d00","service":"sikka-service","timestamp":"2025-09-13T05:50:24.922Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: f79dd8ab-9de4-4ed9-916f-9c2d61293517","service":"sikka-service","timestamp":"2025-09-13T05:50:24.925Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: c93026f2-a853-4305-a266-236a78cc0ef4","service":"sikka-service","timestamp":"2025-09-13T05:50:24.928Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: dc08adc6-02e5-4343-9c75-741f825ebab5","service":"sikka-service","timestamp":"2025-09-13T05:50:24.929Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 1a297154-1849-4fc7-9de2-b4e47fc0eb6d","service":"sikka-service","timestamp":"2025-09-13T05:50:24.934Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: a7c0b4d5-bd95-4bea-affc-cf9019c5c9c0","service":"sikka-service","timestamp":"2025-09-13T05:50:24.936Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 5aa9c117-4055-41a0-b8f2-ca905bd093ba","service":"sikka-service","timestamp":"2025-09-13T05:50:24.938Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: c17092f9-4f79-4cc6-a90e-1c32b9409bbd","service":"sikka-service","timestamp":"2025-09-13T05:50:24.941Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: ae7ac7f5-3005-4a7c-aef3-b539198b220c","service":"sikka-service","timestamp":"2025-09-13T05:50:24.943Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 7a6760ba-6497-44a6-890a-58a7d23a6677","service":"sikka-service","timestamp":"2025-09-13T05:50:24.946Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: ded872e0-c592-42ac-9b05-41d9dc4fde31","service":"sikka-service","timestamp":"2025-09-13T05:50:24.948Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 0a5f2e0d-de44-487e-8810-42857bc1e6ea","service":"sikka-service","timestamp":"2025-09-13T05:50:24.950Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 1e71b6df-3a98-4188-b3ee-7c45f9cd1438","service":"sikka-service","timestamp":"2025-09-13T05:50:24.952Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 83efc642-e1c9-4a96-bc00-6fad4e2d6327","service":"sikka-service","timestamp":"2025-09-13T05:50:24.954Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: ecd3776d-79bc-41f6-8031-36fab3a8b706","service":"sikka-service","timestamp":"2025-09-13T05:50:24.956Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: cd33a1a8-f71c-411c-a31c-224b02938b17","service":"sikka-service","timestamp":"2025-09-13T05:50:24.959Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: ********-bba3-4c11-819e-31bf33953a5c","service":"sikka-service","timestamp":"2025-09-13T05:50:24.961Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 70df38e4-9d39-46da-a334-ea3e1569979a","service":"sikka-service","timestamp":"2025-09-13T05:50:24.963Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 4c6a69c6-c5b3-48b9-8ebd-3db193608825","service":"sikka-service","timestamp":"2025-09-13T05:50:24.964Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 2da85ee3-2302-468a-8dd8-edca7c6aea5a","service":"sikka-service","timestamp":"2025-09-13T05:50:24.966Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 2b91f986-2625-4ed1-80b3-21e98f784e48","service":"sikka-service","timestamp":"2025-09-13T05:50:24.969Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 448cc741-6c76-4e63-a1bd-54a5a5b91bf8","service":"sikka-service","timestamp":"2025-09-13T05:50:24.971Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 9becd539-b27b-4774-bd0e-74a2435352dc","service":"sikka-service","timestamp":"2025-09-13T05:50:24.973Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 1c99e6ae-6ca5-40e7-ad6d-09f4571b2584","service":"sikka-service","timestamp":"2025-09-13T05:50:24.976Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 21fb4a2e-0b57-48fa-b4b1-9d5c56fcc8bf","service":"sikka-service","timestamp":"2025-09-13T05:50:24.979Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 2ce95089-3b65-4ae7-80e1-9bd8ec9fc785","service":"sikka-service","timestamp":"2025-09-13T05:50:24.981Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 181d73cf-ca9c-4cd6-8e16-e8cce360bbfb","service":"sikka-service","timestamp":"2025-09-13T05:50:24.982Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 0feaa29e-6afd-4d79-be61-741e7eafe490","service":"sikka-service","timestamp":"2025-09-13T05:50:24.984Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 66141ef2-c494-4582-ada9-1a2cec5bb07d","service":"sikka-service","timestamp":"2025-09-13T05:50:24.986Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: e7362081-b307-406d-abfa-f8713da28ec0","service":"sikka-service","timestamp":"2025-09-13T05:50:24.988Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: c4e7b852-978e-4db5-942d-72181561ab48","service":"sikka-service","timestamp":"2025-09-13T05:50:24.990Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 8d551291-6688-4842-8d00-7bd740576257","service":"sikka-service","timestamp":"2025-09-13T05:50:24.992Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 868cd37b-6af1-4f0a-ad50-6605b05d3f1b","service":"sikka-service","timestamp":"2025-09-13T05:50:24.994Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 292c261e-5a50-4182-a062-66ffd115eb30","service":"sikka-service","timestamp":"2025-09-13T05:50:24.996Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 60cafbff-d7f2-4fbb-be53-5c0d1b03640b","service":"sikka-service","timestamp":"2025-09-13T05:50:24.998Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 987be72b-0030-4a0d-bcbb-76f55bdbef01","service":"sikka-service","timestamp":"2025-09-13T05:50:25.000Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 17d6a65b-122f-4682-97a8-294bae980c89","service":"sikka-service","timestamp":"2025-09-13T05:50:25.002Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: f89f9d8b-3f19-48f6-be10-c8700b49b3e7","service":"sikka-service","timestamp":"2025-09-13T05:50:25.003Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 3a6fd4be-d922-485b-89ae-094ee327c335","service":"sikka-service","timestamp":"2025-09-13T05:50:25.006Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 669b0ff9-28ec-4f24-8938-929eb49c39fa","service":"sikka-service","timestamp":"2025-09-13T05:50:25.008Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: bd86c0ac-e6d9-4a31-84c3-29e5ecff0c9f","service":"sikka-service","timestamp":"2025-09-13T05:50:25.009Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 615164c4-25ab-4130-813a-f65d0222b524","service":"sikka-service","timestamp":"2025-09-13T05:50:25.011Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: ddab49c3-ffed-4d6c-a4d7-97170b3cd0f1","service":"sikka-service","timestamp":"2025-09-13T05:50:25.014Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 4590fa7b-13e0-4252-8f02-b9c7f8beddb3","service":"sikka-service","timestamp":"2025-09-13T05:50:25.016Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 3875068b-7fd2-4ac1-8fc0-002f697cc450","service":"sikka-service","timestamp":"2025-09-13T05:50:25.018Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: ********-ceb0-44f5-a6a2-ca61f4e1edc8","service":"sikka-service","timestamp":"2025-09-13T05:50:25.020Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: dcb5d5a2-2012-47c7-bbff-1710c1bbd575","service":"sikka-service","timestamp":"2025-09-13T05:50:25.022Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: a778d6a6-c10f-46a0-ac17-fe90765fffa7","service":"sikka-service","timestamp":"2025-09-13T05:50:25.023Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: fb915ac1-c97e-4c67-a9e8-e303d2a6a196","service":"sikka-service","timestamp":"2025-09-13T05:50:25.025Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 1c85ae52-c5c7-4bb7-9552-61aeb6941e4d","service":"sikka-service","timestamp":"2025-09-13T05:50:25.026Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-13T05:51:37.180Z"}
{"label":"sikka-service","level":"info","message":"Calling GET /accounts_receivables?practice_id=1","service":"sikka-service","timestamp":"2025-09-13T05:51:37.317Z"}
{"label":"sikka-service","level":"info","message":"Account receivables fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-13T05:51:38.427Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: dde518eb-46ab-42c9-8428-ff71e502ab10","service":"sikka-service","timestamp":"2025-09-13T05:51:38.463Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 267ba360-3b6a-49cb-a9e8-45c76c6a241a","service":"sikka-service","timestamp":"2025-09-13T05:51:38.465Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: c5f93626-570c-4edb-94a4-37c172170aeb","service":"sikka-service","timestamp":"2025-09-13T05:51:38.467Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 369571d1-fe23-4418-8d27-da940bb14152","service":"sikka-service","timestamp":"2025-09-13T05:51:38.468Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: a0fa0a7a-111e-483f-a0de-2839b43f53a1","service":"sikka-service","timestamp":"2025-09-13T05:51:38.470Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: c5728449-002f-436e-b432-73fbc9ad5c02","service":"sikka-service","timestamp":"2025-09-13T05:51:38.472Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: a2a3dd85-baff-4018-abd5-58886c1dc68e","service":"sikka-service","timestamp":"2025-09-13T05:51:38.474Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 15c7f4cd-cfc0-478f-b58c-7c9c3616727a","service":"sikka-service","timestamp":"2025-09-13T05:51:38.476Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: af413b3e-4c5c-4d8e-90be-7e3efa96e329","service":"sikka-service","timestamp":"2025-09-13T05:51:38.478Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 63dd22ff-1804-4f0d-81f8-e15aa038ab30","service":"sikka-service","timestamp":"2025-09-13T05:51:38.480Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: bf2b6858-54a2-4151-8212-c10bb5b28313","service":"sikka-service","timestamp":"2025-09-13T05:51:38.482Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: c4bac057-8194-487d-8d69-1a57df132daa","service":"sikka-service","timestamp":"2025-09-13T05:51:38.484Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: fb918226-9dd5-489f-8a9a-1b082733ca05","service":"sikka-service","timestamp":"2025-09-13T05:51:38.486Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: a5c8bb8a-76b8-4e73-a163-9726b75007ea","service":"sikka-service","timestamp":"2025-09-13T05:51:38.488Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 526d0a05-359e-4097-aaf7-29bd68cd4803","service":"sikka-service","timestamp":"2025-09-13T05:51:38.489Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 364315a8-eb9e-44a6-967c-a75e246b13a5","service":"sikka-service","timestamp":"2025-09-13T05:51:38.491Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: ac1a7531-13cd-4c14-8f69-59426b631ad1","service":"sikka-service","timestamp":"2025-09-13T05:51:38.493Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 43dc6bb3-2481-472f-b5df-da4dd8f1783b","service":"sikka-service","timestamp":"2025-09-13T05:51:38.495Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: ede58443-f246-4c85-afa8-32d52e80b595","service":"sikka-service","timestamp":"2025-09-13T05:51:38.497Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 14842e2a-caaa-4f00-b70d-51190aaf4eca","service":"sikka-service","timestamp":"2025-09-13T05:51:38.499Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: aa960121-c7e2-4952-b045-9c99aa39a80a","service":"sikka-service","timestamp":"2025-09-13T05:51:38.501Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 76ef3b12-afc8-46a5-9dfa-94cce2562a3b","service":"sikka-service","timestamp":"2025-09-13T05:51:38.502Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 1e9c8d65-fd8f-4fe3-85b1-deea613ee1a8","service":"sikka-service","timestamp":"2025-09-13T05:51:38.504Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 5226e8a4-c49f-400b-97cc-1a4ae97c5201","service":"sikka-service","timestamp":"2025-09-13T05:51:38.505Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 0ee1f917-760b-412b-b65e-0783960d2cad","service":"sikka-service","timestamp":"2025-09-13T05:51:38.507Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: b4be0dd7-b431-4e9b-a5f6-878e7a7961b3","service":"sikka-service","timestamp":"2025-09-13T05:51:38.509Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 6f9dd025-6972-4923-b370-f5e6373d1169","service":"sikka-service","timestamp":"2025-09-13T05:51:38.511Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: ac4a2a48-a2e4-4725-882a-8872948a5518","service":"sikka-service","timestamp":"2025-09-13T05:51:38.514Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: cc45f3fa-1453-42f3-a4ef-90fd14a96664","service":"sikka-service","timestamp":"2025-09-13T05:51:38.516Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 603c22a6-403a-4df2-9628-0d592249ecb6","service":"sikka-service","timestamp":"2025-09-13T05:51:38.517Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 5094e07e-4c2b-4f4c-9fac-842dab8521ac","service":"sikka-service","timestamp":"2025-09-13T05:51:38.519Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 34915dc0-c38d-4d4d-b321-696003f65735","service":"sikka-service","timestamp":"2025-09-13T05:51:38.521Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: a58a0798-a423-4c09-80c8-67cd07bbd255","service":"sikka-service","timestamp":"2025-09-13T05:51:38.523Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: da46b88c-d3f6-4846-858e-f3fd78295f74","service":"sikka-service","timestamp":"2025-09-13T05:51:38.524Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: d86b9ea7-5203-455d-b434-69f8173b0a7d","service":"sikka-service","timestamp":"2025-09-13T05:51:38.527Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 9ac5811a-0c1a-47a0-a5e9-7bebcfe2df62","service":"sikka-service","timestamp":"2025-09-13T05:51:38.528Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 1879f6d0-c671-4c89-9327-d060d644396a","service":"sikka-service","timestamp":"2025-09-13T05:51:38.531Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: a365f6e6-039d-4ba2-b9bf-f3890b994813","service":"sikka-service","timestamp":"2025-09-13T05:51:38.532Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: f6608e80-35b4-4526-ab30-39415a5b57f4","service":"sikka-service","timestamp":"2025-09-13T05:51:38.534Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: dd759b91-205e-4013-bf08-6fd264ba6bed","service":"sikka-service","timestamp":"2025-09-13T05:51:38.536Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: f53cdb58-5f5f-4fb2-a5ee-c22e70cf21ab","service":"sikka-service","timestamp":"2025-09-13T05:51:38.538Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 3904099a-9af2-45da-a2b8-75c89b281f80","service":"sikka-service","timestamp":"2025-09-13T05:51:38.541Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 71e3e075-00ff-4831-8830-354cb8b8c1ba","service":"sikka-service","timestamp":"2025-09-13T05:51:38.544Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 13371c04-5014-4f59-ac1a-f9a18314743a","service":"sikka-service","timestamp":"2025-09-13T05:51:38.545Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 903a9760-001f-4837-9cb2-139711aeae57","service":"sikka-service","timestamp":"2025-09-13T05:51:38.547Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 1629b733-f1f7-4a1f-9b3b-33d40223fa83","service":"sikka-service","timestamp":"2025-09-13T05:51:38.549Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 9ab93845-eefa-44e6-934a-9af9977f3042","service":"sikka-service","timestamp":"2025-09-13T05:51:38.552Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: f77e25b4-e221-4022-8e16-cea33dd57d70","service":"sikka-service","timestamp":"2025-09-13T05:51:38.554Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 4d4c6c0a-0fbe-4974-964d-dfb1ee3bf768","service":"sikka-service","timestamp":"2025-09-13T05:51:38.556Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 77af97e1-3418-4c60-898a-dbd83dbd9149","service":"sikka-service","timestamp":"2025-09-13T05:51:38.558Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-13T05:51:55.460Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-13T05:51:55.591Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-13T05:51:55.592Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-13T05:51:56.659Z"}
{"label":"sikka-service","level":"info","message":"Calling POST /request_key","service":"sikka-service","timestamp":"2025-09-13T05:51:56.660Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","service":"sikka-service","status":200,"timestamp":"2025-09-13T05:51:57.021Z"}
{"label":"sikka-controller","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-13T05:51:57.022Z"}
{"level":"info","message":"sikka_request_key updated successfully with ID: a90af7ba-5b2a-41da-9d3a-b4ad7862fd28","service":"sikka-service","timestamp":"2025-09-13T05:51:57.036Z"}
{"error":"Cannot read properties of undefined (reading 'request_key')","label":"sikka-controller","level":"error","message":"Failed to fetch account receivables","service":"sikka-service","timestamp":"2025-09-13T05:51:57.037Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-13T05:52:38.293Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-13T05:52:46.738Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-13T05:52:47.090Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-13T05:52:47.192Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-13T05:52:47.196Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-13T05:52:47.197Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-13T05:52:47.197Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-13T05:52:47.198Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-13T05:52:50.950Z"}
{"label":"sikka-service","level":"info","message":"Calling GET /accounts_receivables?practice_id=1","service":"sikka-service","timestamp":"2025-09-13T05:52:50.978Z"}
{"label":"sikka-service","level":"info","message":"Account receivables fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-13T05:52:52.227Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: ffea6799-86cd-47dc-84fe-1455a2aad16c","service":"sikka-service","timestamp":"2025-09-13T05:52:52.272Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 763d91b8-286b-43b9-9523-26ad72c8569f","service":"sikka-service","timestamp":"2025-09-13T05:52:52.275Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 4b73f607-1c3c-4681-8f23-aa49dcb0f758","service":"sikka-service","timestamp":"2025-09-13T05:52:52.278Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 8e516825-6bcc-4d90-b937-e9f649f8322d","service":"sikka-service","timestamp":"2025-09-13T05:52:52.280Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 5c262695-0bf8-4e1b-abac-84e0d8a742f4","service":"sikka-service","timestamp":"2025-09-13T05:52:52.281Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 557ba4e5-eea4-457f-8650-96ebfe9c9b96","service":"sikka-service","timestamp":"2025-09-13T05:52:52.283Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 98afe422-6b79-48f3-8029-578caa193b93","service":"sikka-service","timestamp":"2025-09-13T05:52:52.285Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 7e822f85-5123-433e-8b11-8edec4397659","service":"sikka-service","timestamp":"2025-09-13T05:52:52.286Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: d364649a-8062-456b-95dc-6ee5a67f5cc7","service":"sikka-service","timestamp":"2025-09-13T05:52:52.289Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: fffac0bd-c3ac-4192-b19a-071af5920a74","service":"sikka-service","timestamp":"2025-09-13T05:52:52.291Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 4d8558a6-a86a-4242-94f3-2fd7b7bf3872","service":"sikka-service","timestamp":"2025-09-13T05:52:52.293Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: e3a5d1b7-340f-41db-96a8-43f667805e45","service":"sikka-service","timestamp":"2025-09-13T05:52:52.295Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 0ceb4877-630a-4ee1-aac9-17f74548aa91","service":"sikka-service","timestamp":"2025-09-13T05:52:52.297Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: c25eec4f-8a0b-4d68-b154-c1b0a7375783","service":"sikka-service","timestamp":"2025-09-13T05:52:52.299Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 3ded41da-8825-4bf7-9251-5e24d045b917","service":"sikka-service","timestamp":"2025-09-13T05:52:52.302Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 408edbea-9abe-4726-9273-0bd7ef91e064","service":"sikka-service","timestamp":"2025-09-13T05:52:52.304Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: cbee439e-30d5-4a45-bcba-a2afd1202b75","service":"sikka-service","timestamp":"2025-09-13T05:52:52.306Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 7d3b8fe5-b5f9-412b-ba31-4d57650bfbf5","service":"sikka-service","timestamp":"2025-09-13T05:52:52.308Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: cb32d5d8-7cb4-4851-b6c7-7a6228b231b6","service":"sikka-service","timestamp":"2025-09-13T05:52:52.309Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 9e0ace2d-8aeb-4100-8df5-3c7cf1c72a3b","service":"sikka-service","timestamp":"2025-09-13T05:52:52.312Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: de31d8ef-6794-42f1-8f7c-c789c101bb07","service":"sikka-service","timestamp":"2025-09-13T05:52:52.314Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 339921e5-da93-4784-8f53-0672d1030904","service":"sikka-service","timestamp":"2025-09-13T05:52:52.316Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: fede2c01-bbae-4c0d-9428-870cbcfdae4c","service":"sikka-service","timestamp":"2025-09-13T05:52:52.318Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 51bdeca0-b6ad-4472-a48e-c2a6cda03f38","service":"sikka-service","timestamp":"2025-09-13T05:52:52.321Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: b4e5efde-80ab-48fb-8585-5a12fad4bee5","service":"sikka-service","timestamp":"2025-09-13T05:52:52.323Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: eebc180b-4083-48b9-9e27-161fc16e0864","service":"sikka-service","timestamp":"2025-09-13T05:52:52.325Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: d10cc1fc-5ee6-42fc-91af-25be669546da","service":"sikka-service","timestamp":"2025-09-13T05:52:52.326Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: cbba6ca4-36d4-47b8-83fa-6f0355f5d64c","service":"sikka-service","timestamp":"2025-09-13T05:52:52.328Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: c665b4e1-7efd-4519-be69-70ffb0f88667","service":"sikka-service","timestamp":"2025-09-13T05:52:52.330Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 01ae624e-e175-4c67-9dd9-73c321667e8c","service":"sikka-service","timestamp":"2025-09-13T05:52:52.332Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: b579cfd1-098d-48f3-b2a6-a5a238cda73c","service":"sikka-service","timestamp":"2025-09-13T05:52:52.334Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: dc7ccd29-0a38-4131-ad82-276c411b0d10","service":"sikka-service","timestamp":"2025-09-13T05:52:52.336Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 0a575209-21bb-49cc-9a5b-5226a18c0264","service":"sikka-service","timestamp":"2025-09-13T05:52:52.338Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 5a33a626-d78a-4dfe-a2a9-ce194061e311","service":"sikka-service","timestamp":"2025-09-13T05:52:52.339Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 0b5cf2c2-ac74-4f72-8527-5a62fb1266a7","service":"sikka-service","timestamp":"2025-09-13T05:52:52.341Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 8168afa0-c51f-4a4f-bd40-0c9214ff0942","service":"sikka-service","timestamp":"2025-09-13T05:52:52.343Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 8fa25676-be15-4d15-b2b4-feafb0925f69","service":"sikka-service","timestamp":"2025-09-13T05:52:52.345Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: bd014a58-2973-4bb1-a938-90f35def4970","service":"sikka-service","timestamp":"2025-09-13T05:52:52.348Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: fa560744-1941-4c33-a6ca-771f9573b08a","service":"sikka-service","timestamp":"2025-09-13T05:52:52.349Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: e4baf911-fd77-48eb-9318-7f86709861e8","service":"sikka-service","timestamp":"2025-09-13T05:52:52.352Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 4bbcbe1c-bedd-45ca-ad56-2ce741089b09","service":"sikka-service","timestamp":"2025-09-13T05:52:52.354Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 4efe0db4-f689-4e77-a62e-2399028da988","service":"sikka-service","timestamp":"2025-09-13T05:52:52.356Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: d8a79277-6db9-4472-9324-7566fc36ab90","service":"sikka-service","timestamp":"2025-09-13T05:52:52.360Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 61f4f503-f4c6-464b-a0c5-d9d9dcb5cbb8","service":"sikka-service","timestamp":"2025-09-13T05:52:52.362Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 27951bc2-ead9-4d2b-8c8d-82ce1080d212","service":"sikka-service","timestamp":"2025-09-13T05:52:52.364Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: e8aa04ba-c2cf-4a26-ae73-0689da1d1d7b","service":"sikka-service","timestamp":"2025-09-13T05:52:52.367Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: f234f904-2ce0-451e-9089-c92d07c90e5b","service":"sikka-service","timestamp":"2025-09-13T05:52:52.368Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 5bdff905-1653-4803-8463-0ee2f9241462","service":"sikka-service","timestamp":"2025-09-13T05:52:52.370Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 6051ff70-305f-46c3-a7a4-080798a7a186","service":"sikka-service","timestamp":"2025-09-13T05:52:52.372Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: ad0685c8-ed03-4783-be63-a8dd739b10cc","service":"sikka-service","timestamp":"2025-09-13T05:52:52.373Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-13T05:53:47.451Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-13T05:53:47.614Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-13T05:53:47.615Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-13T05:53:48.720Z"}
{"label":"sikka-service","level":"info","message":"Calling POST /request_key","service":"sikka-service","timestamp":"2025-09-13T05:53:48.721Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","service":"sikka-service","status":200,"timestamp":"2025-09-13T05:53:49.098Z"}
{"label":"sikka-controller","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-13T05:53:49.099Z"}
{"level":"info","message":"sikka_request_key updated successfully with ID: a90af7ba-5b2a-41da-9d3a-b4ad7862fd28","service":"sikka-service","timestamp":"2025-09-13T05:53:49.115Z"}
{"error":"Cannot read properties of undefined (reading 'request_key')","label":"sikka-controller","level":"error","message":"Failed to fetch account receivables","service":"sikka-service","timestamp":"2025-09-13T05:53:49.117Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-13T05:54:22.674Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-13T05:54:23.007Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-13T05:54:23.121Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-13T05:54:23.125Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-13T05:54:23.126Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-13T05:54:23.127Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-13T05:54:23.129Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-13T05:54:53.447Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-13T05:54:53.624Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-13T05:54:53.625Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-13T05:54:54.852Z"}
{"label":"sikka-service","level":"info","message":"Calling POST /request_key","service":"sikka-service","timestamp":"2025-09-13T05:54:54.853Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","service":"sikka-service","status":200,"timestamp":"2025-09-13T05:54:55.230Z"}
{"label":"sikka-controller","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-13T05:54:55.231Z"}
{"level":"info","message":"sikka_request_key updated successfully with ID: a90af7ba-5b2a-41da-9d3a-b4ad7862fd28","service":"sikka-service","timestamp":"2025-09-13T05:54:55.254Z"}
{"label":"sikka-service","level":"info","message":"Calling GET /accounts_receivables?practice_id=1","service":"sikka-service","timestamp":"2025-09-13T05:54:55.257Z"}
{"label":"sikka-service","level":"info","message":"Account receivables fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-13T05:54:55.668Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 34563cae-7280-4ba2-93e9-492fd108bb8a","service":"sikka-service","timestamp":"2025-09-13T05:54:55.715Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 0a2662b9-d27f-4568-baa5-4094da3b6e3d","service":"sikka-service","timestamp":"2025-09-13T05:54:55.716Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: dbe2fc62-3d69-484d-82b7-012881b035df","service":"sikka-service","timestamp":"2025-09-13T05:54:55.719Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: a9f0542f-faeb-4fae-afe3-13a8a6a8827a","service":"sikka-service","timestamp":"2025-09-13T05:54:55.722Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 8a051adb-54d3-4a80-9802-68c1745af170","service":"sikka-service","timestamp":"2025-09-13T05:54:55.724Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: d82cdcde-dcb3-4735-b104-e64fec7f99b1","service":"sikka-service","timestamp":"2025-09-13T05:54:55.727Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: d533551e-6a7c-46c9-96fc-c3a564ce03a7","service":"sikka-service","timestamp":"2025-09-13T05:54:55.729Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: c60b79da-6fe2-4059-807f-63bd19b44e5b","service":"sikka-service","timestamp":"2025-09-13T05:54:55.732Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 7910d608-2c39-4744-a4b4-37c37ab2b5cb","service":"sikka-service","timestamp":"2025-09-13T05:54:55.734Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 78de66e9-5596-49ad-9294-2b2dbd0e481d","service":"sikka-service","timestamp":"2025-09-13T05:54:55.736Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 3a8b5bd0-bfcf-4c86-a7f0-d90b2ca8e939","service":"sikka-service","timestamp":"2025-09-13T05:54:55.738Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 4d988b71-5474-47de-96fb-082e62632a60","service":"sikka-service","timestamp":"2025-09-13T05:54:55.740Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 2cdc59ac-15ec-4b8d-8cdd-4e9bc974e0dd","service":"sikka-service","timestamp":"2025-09-13T05:54:55.742Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 5e49898f-5920-4c61-b7b8-6668bb961abc","service":"sikka-service","timestamp":"2025-09-13T05:54:55.744Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: b2aa6f0e-c561-404d-9f92-8e146a849fca","service":"sikka-service","timestamp":"2025-09-13T05:54:55.747Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: a6947158-e7f3-4c29-b40e-************","service":"sikka-service","timestamp":"2025-09-13T05:54:55.749Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 4c4fce1b-b4a6-48b2-b70c-1825986671b2","service":"sikka-service","timestamp":"2025-09-13T05:54:55.751Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 66d7ce8d-0df6-4772-94db-5d46db33db9e","service":"sikka-service","timestamp":"2025-09-13T05:54:55.753Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 1fc92248-ff09-4a9e-a74a-72fd399f9743","service":"sikka-service","timestamp":"2025-09-13T05:54:55.755Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: cfc7fafa-9902-4cf8-b1f2-e79b05723ceb","service":"sikka-service","timestamp":"2025-09-13T05:54:55.757Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 80b38721-e864-4f8e-ad4f-8b4d636a3183","service":"sikka-service","timestamp":"2025-09-13T05:54:55.759Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: ********-84e8-4f9a-9573-cb9824a3998b","service":"sikka-service","timestamp":"2025-09-13T05:54:55.761Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 1a7bcf09-a1d3-45c5-b559-916f2b05a17b","service":"sikka-service","timestamp":"2025-09-13T05:54:55.763Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: cdce120b-9970-4fab-ad2f-c725f957cefe","service":"sikka-service","timestamp":"2025-09-13T05:54:55.767Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 645be275-bc56-4687-83d2-7668430ced9b","service":"sikka-service","timestamp":"2025-09-13T05:54:55.769Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: ********-7325-446a-b8cd-3685cd3719c2","service":"sikka-service","timestamp":"2025-09-13T05:54:55.771Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 0dfd6261-9117-4398-bb57-bd325e050569","service":"sikka-service","timestamp":"2025-09-13T05:54:55.778Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 68f0e00c-1854-4081-9116-ebd79e1ec54a","service":"sikka-service","timestamp":"2025-09-13T05:54:55.780Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 1e5eb83f-3c35-45d0-88fa-6f319b4d7f6e","service":"sikka-service","timestamp":"2025-09-13T05:54:55.783Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: fcbb7cbd-1ad8-4bce-a36a-9b3bcffcba85","service":"sikka-service","timestamp":"2025-09-13T05:54:55.785Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: aebec8c5-6db7-4887-9417-1ebf869d6a3e","service":"sikka-service","timestamp":"2025-09-13T05:54:55.787Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: a8733918-3ab3-46b5-822e-46d1dea664d1","service":"sikka-service","timestamp":"2025-09-13T05:54:55.789Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: e25aaca3-0e49-4501-8805-c2e95e238a2c","service":"sikka-service","timestamp":"2025-09-13T05:54:55.791Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: c3594591-09f3-4b07-aaae-bdfa6132e382","service":"sikka-service","timestamp":"2025-09-13T05:54:55.793Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: e309d34c-8e15-4ce7-aceb-2e377c2cd717","service":"sikka-service","timestamp":"2025-09-13T05:54:55.795Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: de2f96ab-07cd-4d67-af13-0bd1420a9db8","service":"sikka-service","timestamp":"2025-09-13T05:54:55.797Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: c86e2a97-fc13-44b4-867b-8e8d462298dd","service":"sikka-service","timestamp":"2025-09-13T05:54:55.799Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: d84b837a-0145-4794-85e5-b543510f5009","service":"sikka-service","timestamp":"2025-09-13T05:54:55.801Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 2f758917-8971-4f04-9032-b874cc76f0af","service":"sikka-service","timestamp":"2025-09-13T05:54:55.803Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: d2c944cf-c645-438f-bc48-297bb24756c8","service":"sikka-service","timestamp":"2025-09-13T05:54:55.805Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 7a352409-bb82-4b29-a0af-dee4342f5524","service":"sikka-service","timestamp":"2025-09-13T05:54:55.807Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 8cffa6cd-ddcf-4d1c-b33b-efa1c62f1fe1","service":"sikka-service","timestamp":"2025-09-13T05:54:55.810Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 16d9773a-1c71-4493-9963-71f103348ee8","service":"sikka-service","timestamp":"2025-09-13T05:54:55.812Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: b290ece4-294d-4d1f-96bd-fd4a41d09037","service":"sikka-service","timestamp":"2025-09-13T05:54:55.815Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 353cb862-b947-47ea-880e-e2792acb5fa5","service":"sikka-service","timestamp":"2025-09-13T05:54:55.817Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: f3a52e29-997e-437c-bd64-e47a6e6061cc","service":"sikka-service","timestamp":"2025-09-13T05:54:55.818Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: f0e9825b-b1c0-4080-8c3b-c313ad2bbe6d","service":"sikka-service","timestamp":"2025-09-13T05:54:55.820Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: 54ee0f68-6c3f-45b6-ad49-289b9fc449bb","service":"sikka-service","timestamp":"2025-09-13T05:54:55.822Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: de598e24-52f4-4dbd-8884-3850e746ba91","service":"sikka-service","timestamp":"2025-09-13T05:54:55.824Z"}
{"level":"info","message":"sikka_accounts_receivable created successfully with ID: eaf4ee3b-39ef-4f19-9984-5a3c1c472c94","service":"sikka-service","timestamp":"2025-09-13T05:54:55.826Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-13T05:55:44.301Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-13T05:55:44.697Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-13T05:55:44.806Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-13T05:55:44.810Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-13T05:55:44.810Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-13T05:55:44.811Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-13T05:55:44.812Z"}
{"label":"sikka-server","level":"info","message":"SIGINT received, shutting down gracefully","service":"sikka-service","timestamp":"2025-09-13T05:55:44.839Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-13T06:30:33.517Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-13T06:30:33.889Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-13T06:30:33.996Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-13T06:30:33.999Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-13T06:30:34.000Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-13T06:30:34.001Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-13T06:30:34.001Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-13T06:30:45.199Z"}
{"label":"sikka-service","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-13T06:30:45.200Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-13T06:30:45.202Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-13T06:30:46.453Z"}
{"label":"sikka-service","level":"info","message":"Calling POST /request_key","service":"sikka-service","timestamp":"2025-09-13T06:30:46.454Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","service":"sikka-service","status":200,"timestamp":"2025-09-13T06:30:47.079Z"}
{"label":"sikka-repository","level":"info","message":"Finding RequestKey","service":"sikka-service","timestamp":"2025-09-13T06:30:47.080Z","where":{"office_id":"D19408"}}
{"id":"a90af7ba-5b2a-41da-9d3a-b4ad7862fd28","label":"sikka-repository","level":"info","message":"Updating RequestKey","service":"sikka-service","timestamp":"2025-09-13T06:30:47.230Z"}
{"error":"invalid input syntax for type integer: \"86400 second(s)\"","label":"sikka-repository","level":"error","message":"Error updating RequestKey","service":"sikka-service","timestamp":"2025-09-13T06:30:47.253Z"}
{"error":"invalid input syntax for type integer: \"86400 second(s)\"","label":"sikka-service","level":"error","message":"Failed to generate request key","service":"sikka-service","timestamp":"2025-09-13T06:30:47.254Z"}
{"error":"invalid input syntax for type integer: \"86400 second(s)\"","label":"sikka-controller","level":"error","message":"Failed to generate request key","service":"sikka-service","timestamp":"2025-09-13T06:30:47.255Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-13T06:31:31.315Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-13T06:31:31.708Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-13T06:31:31.796Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-13T06:31:31.799Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-13T06:31:31.800Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-13T06:31:31.801Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-13T06:31:31.801Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-13T06:31:38.209Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-13T06:31:39.145Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-13T06:31:39.392Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-13T06:31:39.397Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-13T06:31:39.398Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-13T06:31:39.399Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-13T06:31:39.400Z"}
{"errors":[{"field":"app_id","location":"body","message":"App ID is required"},{"field":"app_id","location":"body","message":"App ID must be between 1 and 100 characters"},{"field":"app_key","location":"body","message":"App Key is required"},{"field":"app_key","location":"body","message":"App Key must be between 1 and 255 characters"}],"label":"validation-middleware","level":"warn","message":"Validation failed","method":"POST","path":"/request-key","service":"sikka-service","timestamp":"2025-09-13T06:31:42.503Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-13T06:32:25.179Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-13T06:32:25.509Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-13T06:32:25.601Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-13T06:32:25.605Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-13T06:32:25.605Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-13T06:32:25.606Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-13T06:32:25.607Z"}
{"errors":[{"field":"app_id","location":"body","message":"App ID is required"},{"field":"app_id","location":"body","message":"App ID must be between 1 and 100 characters"},{"field":"app_key","location":"body","message":"App Key is required"},{"field":"app_key","location":"body","message":"App Key must be between 1 and 255 characters"}],"label":"validation-middleware","level":"warn","message":"Validation failed","method":"POST","path":"/request-key","service":"sikka-service","timestamp":"2025-09-13T06:33:01.049Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-13T06:33:17.615Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-13T06:33:18.908Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-13T06:33:19.210Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-13T06:33:19.214Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-13T06:33:19.214Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-13T06:33:19.215Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-13T06:33:19.215Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-13T06:33:23.390Z"}
{"label":"sikka-service","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-13T06:33:23.391Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-13T06:33:23.393Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-13T06:33:24.575Z"}
{"label":"sikka-service","level":"info","message":"Calling POST /request_key","service":"sikka-service","timestamp":"2025-09-13T06:33:24.576Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","service":"sikka-service","status":200,"timestamp":"2025-09-13T06:33:25.000Z"}
{"label":"sikka-repository","level":"info","message":"Finding RequestKey","service":"sikka-service","timestamp":"2025-09-13T06:33:25.000Z","where":{"office_id":"D19408"}}
{"id":"a90af7ba-5b2a-41da-9d3a-b4ad7862fd28","label":"sikka-repository","level":"info","message":"Updating RequestKey","service":"sikka-service","timestamp":"2025-09-13T06:33:25.027Z"}
{"level":"info","message":"sikka_request_key updated successfully with ID: a90af7ba-5b2a-41da-9d3a-b4ad7862fd28","service":"sikka-service","timestamp":"2025-09-13T06:33:25.056Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-13T06:33:25.057Z"}
{"label":"sikka-controller","level":"info","message":"Request key generated successfully","service":"sikka-service","timestamp":"2025-09-13T06:33:25.058Z"}
{"label":"sikka-controller","level":"info","message":"Account receivables fetched successfully","service":"sikka-service","timestamp":"2025-09-13T06:35:15.433Z"}
{"label":"sikka-service","level":"info","message":"Account receivables fetched successfully","service":"sikka-service","timestamp":"2025-09-13T06:35:15.433Z"}
{"label":"sikka-repository","level":"info","message":"Finding RequestKey","service":"sikka-service","timestamp":"2025-09-13T06:35:15.434Z","where":{"office_id":"D19408"}}
{"label":"sikka-service","level":"info","message":"Using existing valid request key","service":"sikka-service","timestamp":"2025-09-13T06:35:15.595Z"}
{"label":"sikka-service","level":"info","message":"Calling GET /accounts_receivables?practice_id=1","service":"sikka-service","timestamp":"2025-09-13T06:35:15.595Z"}
{"label":"sikka-service","level":"info","message":"Account receivables fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-13T06:35:16.757Z"}
{"count":50,"label":"sikka-repository","level":"info","message":"Bulk creating AccountsReceivable","service":"sikka-service","timestamp":"2025-09-13T06:35:16.758Z"}
{"level":"info","message":"Bulk created 50 sikka_accounts_receivable records","service":"sikka-service","timestamp":"2025-09-13T06:35:16.804Z"}
{"label":"sikka-service","level":"info","message":"Account receivables fetched successfully","service":"sikka-service","timestamp":"2025-09-13T06:35:16.804Z"}
{"label":"sikka-controller","level":"info","message":"Account receivables fetched successfully","service":"sikka-service","timestamp":"2025-09-13T06:35:16.805Z"}
{"label":"sikka-controller","level":"info","message":"Account receivables fetched successfully","service":"sikka-service","timestamp":"2025-09-13T06:35:58.708Z"}
{"label":"sikka-service","level":"info","message":"Account receivables fetched successfully","service":"sikka-service","timestamp":"2025-09-13T06:35:58.709Z"}
{"label":"sikka-repository","level":"info","message":"Finding RequestKey","service":"sikka-service","timestamp":"2025-09-13T06:35:58.709Z","where":{"office_id":"D19408"}}
{"label":"sikka-service","level":"info","message":"Using existing valid request key","service":"sikka-service","timestamp":"2025-09-13T06:35:58.847Z"}
{"label":"sikka-service","level":"info","message":"Calling GET /accounts_receivables?practice_id=1","service":"sikka-service","timestamp":"2025-09-13T06:35:58.847Z"}
{"label":"sikka-service","level":"info","message":"Account receivables fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-13T06:36:00.065Z"}
{"count":50,"label":"sikka-repository","level":"info","message":"Bulk creating AccountsReceivable","service":"sikka-service","timestamp":"2025-09-13T06:36:00.065Z"}
{"level":"info","message":"Bulk created 50 sikka_accounts_receivable records","service":"sikka-service","timestamp":"2025-09-13T06:36:00.091Z"}
{"label":"sikka-service","level":"info","message":"Account receivables fetched successfully","service":"sikka-service","timestamp":"2025-09-13T06:36:00.091Z"}
{"label":"sikka-controller","level":"info","message":"Account receivables fetched successfully","service":"sikka-service","timestamp":"2025-09-13T06:36:00.092Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-13T06:38:45.959Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-13T06:38:46.340Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-13T06:38:46.445Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-13T06:38:46.450Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-13T06:38:46.451Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-13T06:38:46.452Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-13T06:38:46.452Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-13T06:39:08.769Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-13T06:39:09.168Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-13T06:39:09.261Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-13T06:39:09.264Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-13T06:39:09.265Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-13T06:39:09.265Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-13T06:39:09.266Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-13T06:40:14.375Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-13T06:40:14.725Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-13T06:40:14.817Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-13T06:40:14.821Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-13T06:40:14.821Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-13T06:40:14.822Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-13T06:40:14.823Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-13T06:42:42.762Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-13T06:42:43.918Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-13T06:42:44.327Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-13T06:42:44.331Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-13T06:42:44.332Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-13T06:42:44.332Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-13T06:42:44.333Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-13T06:43:51.047Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-13T06:43:51.502Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-13T06:43:51.617Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-13T06:43:51.621Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-13T06:43:51.621Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-13T06:43:51.622Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-13T06:43:51.623Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-13T06:44:44.906Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-13T06:44:45.273Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-13T06:44:45.380Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-13T06:44:45.385Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-13T06:44:45.385Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-13T06:44:45.387Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-13T06:44:45.387Z"}
{"label":"sikka-server","level":"info","message":"SIGINT received, shutting down gracefully","service":"sikka-service","timestamp":"2025-09-13T06:44:47.539Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-15T05:22:55.153Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-15T05:22:55.646Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-15T05:22:55.919Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-15T05:22:55.923Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-15T05:22:55.923Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-15T05:22:55.924Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-15T05:22:55.925Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-15T05:23:11.893Z"}
{"label":"sikka-service","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-15T05:23:11.895Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-15T05:23:11.897Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-15T05:23:12.796Z"}
{"label":"sikka-service","level":"info","message":"Calling POST /request_key","service":"sikka-service","timestamp":"2025-09-15T05:23:12.797Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","service":"sikka-service","status":200,"timestamp":"2025-09-15T05:23:13.079Z"}
{"label":"sikka-repository","level":"info","message":"Finding RequestKey","service":"sikka-service","timestamp":"2025-09-15T05:23:13.080Z","where":{"office_id":"D19408"}}
{"id":"a90af7ba-5b2a-41da-9d3a-b4ad7862fd28","label":"sikka-repository","level":"info","message":"Updating RequestKey","service":"sikka-service","timestamp":"2025-09-15T05:23:13.222Z"}
{"level":"info","message":"sikka_request_key updated successfully with ID: a90af7ba-5b2a-41da-9d3a-b4ad7862fd28","service":"sikka-service","timestamp":"2025-09-15T05:23:13.257Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-15T05:23:13.258Z"}
{"label":"sikka-controller","level":"info","message":"Request key generated successfully","service":"sikka-service","timestamp":"2025-09-15T05:23:13.259Z"}
{"label":"sikka-server","level":"info","message":"SIGINT received, shutting down gracefully","service":"sikka-service","timestamp":"2025-09-15T05:35:36.022Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-15T06:37:00.842Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-15T06:37:01.190Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-15T06:37:01.433Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-15T06:37:01.437Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-15T06:37:01.438Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-15T06:37:01.439Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-15T06:37:01.439Z"}
{"label":"sikka-server","level":"info","message":"SIGINT received, shutting down gracefully","service":"sikka-service","timestamp":"2025-09-15T06:37:06.386Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-15T06:48:49.497Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-15T06:48:49.814Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-15T06:48:49.994Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-15T06:48:49.997Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-15T06:48:49.998Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-15T06:48:49.998Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-15T06:48:49.999Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-15T06:49:31.245Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-15T06:49:31.602Z"}
{"label":"sikka-server","level":"info","message":"Database synchronized successfully","service":"sikka-service","timestamp":"2025-09-15T06:49:31.744Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-15T06:49:31.747Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-15T06:49:31.748Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-15T06:49:31.749Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-15T06:49:31.750Z"}
{"label":"sikka-server","level":"info","message":"SIGINT received, shutting down gracefully","service":"sikka-service","timestamp":"2025-09-15T06:49:49.493Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-15T07:02:24.951Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-15T07:02:25.243Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-15T07:02:25.247Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-15T07:02:25.247Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-15T07:02:25.248Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-15T07:02:25.248Z"}
{"label":"sikka-controller","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-15T07:02:39.247Z"}
{"label":"sikka-service","level":"info","message":"Requesting API key from Sikka","service":"sikka-service","timestamp":"2025-09-15T07:02:39.248Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Calling GET /authorized_practices","service":"sikka-service","timestamp":"2025-09-15T07:02:39.249Z"}
{"app_id":"2afe8a20e88e812b22248dce3bdc4a77","label":"sikka-service","level":"info","message":"Authorized practices fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-15T07:02:40.257Z"}
{"label":"sikka-service","level":"info","message":"Calling POST /request_key","service":"sikka-service","timestamp":"2025-09-15T07:02:40.259Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","service":"sikka-service","status":200,"timestamp":"2025-09-15T07:02:40.631Z"}
{"label":"sikka-repository","level":"info","message":"Finding RequestKey","service":"sikka-service","timestamp":"2025-09-15T07:02:40.632Z","where":{"office_id":"D19408"}}
{"id":"a90af7ba-5b2a-41da-9d3a-b4ad7862fd28","label":"sikka-repository","level":"info","message":"Updating RequestKey","service":"sikka-service","timestamp":"2025-09-15T07:02:40.779Z"}
{"level":"info","message":"sikka_request_key updated successfully with ID: a90af7ba-5b2a-41da-9d3a-b4ad7862fd28","service":"sikka-service","timestamp":"2025-09-15T07:02:40.801Z"}
{"label":"sikka-service","level":"info","message":"Request key generated successfully","office_id":"D19408","service":"sikka-service","timestamp":"2025-09-15T07:02:40.801Z"}
{"label":"sikka-controller","level":"info","message":"Request key generated successfully","service":"sikka-service","timestamp":"2025-09-15T07:02:40.802Z"}
{"level":"info","message":"Account receivables fetched successfully","service":"sikka-service","timestamp":"2025-09-15T07:03:00.497Z"}
{"label":"kpis-service","level":"info","message":"Account receivables fetched successfully","service":"sikka-service","timestamp":"2025-09-15T07:03:00.498Z"}
{"label":"sikka-repository","level":"info","message":"Finding RequestKey","service":"sikka-service","timestamp":"2025-09-15T07:03:00.499Z","where":{"office_id":"D19408"}}
{"label":"kpis-service","level":"info","message":"Using existing valid request key","service":"sikka-service","timestamp":"2025-09-15T07:03:00.626Z"}
{"label":"sikka-service","level":"info","message":"Calling GET /kpis//accounts_receivables(1)","service":"sikka-service","timestamp":"2025-09-15T07:03:00.627Z"}
{"error":"Request failed with status code 404","label":"sikka-service","level":"error","message":"Request key API call failed","service":"sikka-service","timestamp":"2025-09-15T07:03:01.530Z"}
{"error":"Sikka API Error: 404 - Not Found","label":"kpis-service","level":"error","message":"Failed to fetch account receivables","service":"sikka-service","timestamp":"2025-09-15T07:03:01.531Z"}
{"error":"Sikka API Error: 404 - Not Found","level":"error","message":"Failed to fetch account receivables","service":"sikka-service","timestamp":"2025-09-15T07:03:01.532Z"}
{"error":"Sikka API Error: 404 - Not Found","label":"kpis-service","level":"error","message":"Failed to fetch account receivables","service":"sikka-service","timestamp":"2025-09-15T07:03:01.532Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-15T07:04:14.489Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-15T07:04:14.797Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-15T07:04:14.801Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-15T07:04:14.802Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-15T07:04:14.802Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-15T07:04:14.803Z"}
{"level":"info","message":"Account receivables fetched successfully","service":"sikka-service","timestamp":"2025-09-15T07:04:17.348Z"}
{"label":"kpis-service","level":"info","message":"Account receivables fetched successfully","service":"sikka-service","timestamp":"2025-09-15T07:04:17.349Z"}
{"label":"sikka-repository","level":"info","message":"Finding RequestKey","service":"sikka-service","timestamp":"2025-09-15T07:04:17.350Z","where":{"office_id":"D19408"}}
{"label":"kpis-service","level":"info","message":"Using existing valid request key","service":"sikka-service","timestamp":"2025-09-15T07:04:17.459Z"}
{"label":"sikka-service","level":"info","message":"Calling GET /kpis/accounts_receivables(1)","service":"sikka-service","timestamp":"2025-09-15T07:04:17.461Z"}
{"error":"Request failed with status code 400","label":"sikka-service","level":"error","message":"Request key API call failed","service":"sikka-service","timestamp":"2025-09-15T07:04:18.507Z"}
{"error":"Sikka API Error: 400 - Bad Request","label":"kpis-service","level":"error","message":"Failed to fetch account receivables","service":"sikka-service","timestamp":"2025-09-15T07:04:18.508Z"}
{"error":"Sikka API Error: 400 - Bad Request","level":"error","message":"Failed to fetch account receivables","service":"sikka-service","timestamp":"2025-09-15T07:04:18.509Z"}
{"error":"Sikka API Error: 400 - Bad Request","label":"kpis-service","level":"error","message":"Failed to fetch account receivables","service":"sikka-service","timestamp":"2025-09-15T07:04:18.509Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-15T07:05:11.049Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-15T07:05:11.354Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-15T07:05:11.358Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-15T07:05:11.359Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-15T07:05:11.360Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-15T07:05:11.360Z"}
{"level":"info","message":"Account receivables fetched successfully","service":"sikka-service","timestamp":"2025-09-15T07:05:45.620Z"}
{"label":"kpis-service","level":"info","message":"Account receivables fetched successfully","service":"sikka-service","timestamp":"2025-09-15T07:05:45.621Z"}
{"label":"sikka-repository","level":"info","message":"Finding RequestKey","service":"sikka-service","timestamp":"2025-09-15T07:05:45.622Z","where":{"office_id":"D19408"}}
{"label":"kpis-service","level":"info","message":"Using existing valid request key","service":"sikka-service","timestamp":"2025-09-15T07:05:45.765Z"}
{"label":"sikka-service","level":"info","message":"Calling GET /kpis/accounts_receivables(1)","service":"sikka-service","timestamp":"2025-09-15T07:05:45.766Z"}
{"error":"Request failed with status code 400","label":"sikka-service","level":"error","message":"Request key API call failed","service":"sikka-service","timestamp":"2025-09-15T07:05:46.685Z"}
{"error":"Sikka API Error: 400 - Bad Request","label":"kpis-service","level":"error","message":"Failed to fetch account receivables","service":"sikka-service","timestamp":"2025-09-15T07:05:46.686Z"}
{"error":"Sikka API Error: 400 - Bad Request","level":"error","message":"Failed to fetch account receivables","service":"sikka-service","timestamp":"2025-09-15T07:05:46.686Z"}
{"error":"Sikka API Error: 400 - Bad Request","label":"kpis-service","level":"error","message":"Failed to fetch account receivables","service":"sikka-service","timestamp":"2025-09-15T07:05:46.686Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-15T07:07:09.528Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-15T07:07:10.201Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-15T07:07:10.205Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-15T07:07:10.206Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-15T07:07:10.206Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-15T07:07:10.207Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-15T07:07:35.417Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-15T07:07:36.536Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-15T07:07:36.540Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-15T07:07:36.540Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-15T07:07:36.541Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-15T07:07:36.542Z"}
{"level":"info","message":"Account receivables fetched successfully","service":"sikka-service","timestamp":"2025-09-15T07:07:54.676Z"}
{"label":"kpis-service","level":"info","message":"Account receivables fetched successfully","service":"sikka-service","timestamp":"2025-09-15T07:07:54.676Z"}
{"label":"sikka-repository","level":"info","message":"Finding RequestKey","service":"sikka-service","timestamp":"2025-09-15T07:07:54.678Z","where":{"office_id":"D19408"}}
{"label":"kpis-service","level":"info","message":"Using existing valid request key","service":"sikka-service","timestamp":"2025-09-15T07:07:54.809Z"}
{"label":"sikka-service","level":"info","message":"Calling GET /kpis/accounts_receivables(1)","service":"sikka-service","timestamp":"2025-09-15T07:07:54.810Z"}
{"error":"Request failed with status code 400","label":"sikka-service","level":"error","message":"Request key API call failed","service":"sikka-service","timestamp":"2025-09-15T07:07:55.878Z"}
{"error":"Sikka API Error: 400 - Bad Request","label":"kpis-service","level":"error","message":"Failed to fetch account receivables","service":"sikka-service","timestamp":"2025-09-15T07:07:55.879Z"}
{"error":"Sikka API Error: 400 - Bad Request","level":"error","message":"Failed to fetch account receivables","service":"sikka-service","timestamp":"2025-09-15T07:07:55.880Z"}
{"error":"Sikka API Error: 400 - Bad Request","label":"kpis-service","level":"error","message":"Failed to fetch account receivables","service":"sikka-service","timestamp":"2025-09-15T07:07:55.880Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-15T07:08:33.959Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-15T07:08:35.002Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-15T07:08:35.006Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-15T07:08:35.007Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-15T07:08:35.007Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-15T07:08:35.008Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-15T07:09:09.658Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-15T07:09:09.960Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-15T07:09:09.964Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-15T07:09:09.964Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-15T07:09:09.965Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-15T07:09:09.965Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-15T07:09:42.516Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-15T07:09:44.011Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-15T07:09:44.016Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-15T07:09:44.017Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-15T07:09:44.018Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-15T07:09:44.019Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-15T07:10:10.388Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-15T07:10:11.659Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-15T07:10:11.665Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-15T07:10:11.665Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-15T07:10:11.666Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-15T07:10:11.668Z"}
{"level":"info","message":"Account receivables fetched successfully","service":"sikka-service","timestamp":"2025-09-15T07:10:21.853Z"}
{"label":"kpis-service","level":"info","message":"Account receivables fetched successfully","service":"sikka-service","timestamp":"2025-09-15T07:10:21.854Z"}
{"label":"sikka-repository","level":"info","message":"Finding RequestKey","service":"sikka-service","timestamp":"2025-09-15T07:10:21.855Z","where":{"office_id":"D19408"}}
{"label":"kpis-service","level":"info","message":"Using existing valid request key","service":"sikka-service","timestamp":"2025-09-15T07:10:21.995Z"}
{"label":"sikka-service","level":"info","message":"Calling GET /kpis/accounts_receivables?practice_id=1","service":"sikka-service","timestamp":"2025-09-15T07:10:21.997Z"}
{"error":"Request failed with status code 400","label":"sikka-service","level":"error","message":"Request key API call failed","service":"sikka-service","timestamp":"2025-09-15T07:10:22.884Z"}
{"error":"Sikka API Error: 400 - Bad Request","label":"kpis-service","level":"error","message":"Failed to fetch account receivables","service":"sikka-service","timestamp":"2025-09-15T07:10:22.884Z"}
{"error":"Sikka API Error: 400 - Bad Request","level":"error","message":"Failed to fetch account receivables","service":"sikka-service","timestamp":"2025-09-15T07:10:22.885Z"}
{"error":"Sikka API Error: 400 - Bad Request","label":"kpis-service","level":"error","message":"Failed to fetch account receivables","service":"sikka-service","timestamp":"2025-09-15T07:10:22.885Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-15T07:11:44.543Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-15T07:11:45.665Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-15T07:11:45.669Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-15T07:11:45.670Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-15T07:11:45.670Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-15T07:11:45.671Z"}
{"level":"info","message":"Account receivables fetched successfully","service":"sikka-service","timestamp":"2025-09-15T07:11:52.429Z"}
{"label":"kpis-service","level":"info","message":"Account receivables fetched successfully","service":"sikka-service","timestamp":"2025-09-15T07:11:52.430Z"}
{"label":"sikka-repository","level":"info","message":"Finding RequestKey","service":"sikka-service","timestamp":"2025-09-15T07:11:52.431Z","where":{"office_id":"D19408"}}
{"label":"kpis-service","level":"info","message":"Using existing valid request key","service":"sikka-service","timestamp":"2025-09-15T07:11:52.466Z"}
{"label":"sikka-service","level":"info","message":"Calling GET /kpis/accounts_receivables?practice_id=1","service":"sikka-service","timestamp":"2025-09-15T07:11:52.468Z"}
{"error":"Request failed with status code 400","label":"sikka-service","level":"error","message":"Request key API call failed","service":"sikka-service","timestamp":"2025-09-15T07:11:53.482Z"}
{"error":"Sikka API Error: 400 - Bad Request","label":"kpis-service","level":"error","message":"Failed to fetch account receivables","service":"sikka-service","timestamp":"2025-09-15T07:11:53.482Z"}
{"error":"Sikka API Error: 400 - Bad Request","level":"error","message":"Failed to fetch account receivables","service":"sikka-service","timestamp":"2025-09-15T07:11:53.483Z"}
{"error":"Sikka API Error: 400 - Bad Request","label":"kpis-service","level":"error","message":"Failed to fetch account receivables","service":"sikka-service","timestamp":"2025-09-15T07:11:53.483Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-15T07:13:31.111Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-15T07:13:31.414Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-15T07:13:31.417Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-15T07:13:31.418Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-15T07:13:31.418Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-15T07:13:31.419Z"}
{"level":"info","message":"Account receivables fetched successfully","service":"sikka-service","timestamp":"2025-09-15T07:13:46.995Z"}
{"label":"kpis-service","level":"info","message":"Account receivables fetched successfully","service":"sikka-service","timestamp":"2025-09-15T07:13:46.996Z"}
{"label":"sikka-repository","level":"info","message":"Finding RequestKey","service":"sikka-service","timestamp":"2025-09-15T07:13:46.997Z","where":{"office_id":"D19408"}}
{"label":"kpis-service","level":"info","message":"Using existing valid request key","service":"sikka-service","timestamp":"2025-09-15T07:13:47.131Z"}
{"label":"sikka-service","level":"info","message":"Calling GET /kpis/accounts_receivable?practice_id=1","service":"sikka-service","timestamp":"2025-09-15T07:13:47.133Z"}
{"label":"sikka-service","level":"info","message":"Account receivables fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-15T07:13:48.561Z"}
{"level":"info","message":"Creating AccountsReceivable","service":"sikka-service","timestamp":"2025-09-15T07:13:48.562Z"}
{"level":"info","message":"Bulk created 4 sikka_accounts_receivable records","service":"sikka-service","timestamp":"2025-09-15T07:13:48.584Z"}
{"label":"kpis-service","level":"info","message":"Account receivables fetched successfully","service":"sikka-service","timestamp":"2025-09-15T07:13:48.585Z"}
{"level":"info","message":"Account receivables fetched successfully","service":"sikka-service","timestamp":"2025-09-15T07:13:48.586Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-15T07:15:07.183Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-15T07:15:07.504Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-15T07:15:07.509Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-15T07:15:07.510Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-15T07:15:07.510Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-15T07:15:07.511Z"}
{"label":"sikka-server","level":"info","message":"SIGINT received, shutting down gracefully","service":"sikka-service","timestamp":"2025-09-15T07:15:15.326Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-15T07:20:41.725Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-15T07:20:42.140Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-15T07:20:42.145Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-15T07:20:42.145Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-15T07:20:42.146Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-15T07:20:42.148Z"}
{"level":"info","message":"Account receivables fetched successfully","service":"sikka-service","timestamp":"2025-09-15T07:20:53.204Z"}
{"label":"kpis-service","level":"info","message":"Account receivables fetched successfully","service":"sikka-service","timestamp":"2025-09-15T07:20:53.205Z"}
{"label":"sikka-repository","level":"info","message":"Finding RequestKey","service":"sikka-service","timestamp":"2025-09-15T07:20:53.206Z","where":{"office_id":"D19408"}}
{"label":"kpis-service","level":"info","message":"Using existing valid request key","service":"sikka-service","timestamp":"2025-09-15T07:20:53.410Z"}
{"label":"sikka-service","level":"info","message":"Calling GET /kpis/accounts_receivable?practice_id=1","service":"sikka-service","timestamp":"2025-09-15T07:20:53.412Z"}
{"label":"sikka-service","level":"info","message":"Account receivables fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-15T07:20:55.362Z"}
{"level":"info","message":"Creating AccountsReceivable","service":"sikka-service","timestamp":"2025-09-15T07:20:55.363Z"}
{"level":"info","message":"Bulk created 4 sikka_accounts_receivable records","service":"sikka-service","timestamp":"2025-09-15T07:20:55.417Z"}
{"label":"kpis-service","level":"info","message":"Account receivables fetched successfully","service":"sikka-service","timestamp":"2025-09-15T07:20:55.419Z"}
{"level":"info","message":"Account receivables fetched successfully","service":"sikka-service","timestamp":"2025-09-15T07:20:55.420Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-15T07:26:15.085Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-15T07:26:15.539Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-15T07:26:15.545Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-15T07:26:15.545Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-15T07:26:15.546Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-15T07:26:15.547Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-15T07:27:03.059Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-15T07:27:03.390Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-15T07:27:03.395Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-15T07:27:03.395Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-15T07:27:03.396Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-15T07:27:03.397Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-15T08:46:31.507Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-15T08:46:31.984Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-15T08:46:31.991Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-15T08:46:31.992Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-15T08:46:31.993Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-15T08:46:31.994Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-15T08:47:02.240Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-15T08:47:03.139Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-15T08:47:03.143Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-15T08:47:03.144Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-15T08:47:03.145Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-15T08:47:03.146Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-15T08:50:52.378Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-15T08:50:53.609Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-15T08:50:53.613Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-15T08:50:53.614Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-15T08:50:53.614Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-15T08:50:53.615Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-15T08:51:23.976Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-15T08:51:25.372Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-15T08:51:25.381Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-15T08:51:25.382Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-15T08:51:25.383Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-15T08:51:25.384Z"}
{"level":"info","message":"Treatment analysis fetched successfully","service":"sikka-service","timestamp":"2025-09-15T08:51:35.134Z"}
{"label":"kpis-service","level":"info","message":"Treatment analysis fetched successfully","service":"sikka-service","timestamp":"2025-09-15T08:51:35.135Z"}
{"label":"sikka-repository","level":"info","message":"Finding RequestKey","service":"sikka-service","timestamp":"2025-09-15T08:51:35.136Z","where":{}}
{"error":"WHERE parameter \"office_id\" has invalid \"undefined\" value","level":"error","message":"Failed to fetch treatment analysis","service":"sikka-service","timestamp":"2025-09-15T08:51:35.145Z"}
{"error":"WHERE parameter \"office_id\" has invalid \"undefined\" value","label":"kpis-service","level":"error","message":"Failed to fetch treatment analysis","service":"sikka-service","timestamp":"2025-09-15T08:51:35.144Z"}
{"error":"WHERE parameter \"office_id\" has invalid \"undefined\" value","label":"sikka-repository","level":"error","message":"Error finding RequestKey","service":"sikka-service","timestamp":"2025-09-15T08:51:35.143Z"}
{"level":"info","message":"Treatment analysis fetched successfully","service":"sikka-service","timestamp":"2025-09-15T08:52:20.440Z"}
{"label":"kpis-service","level":"info","message":"Treatment analysis fetched successfully","service":"sikka-service","timestamp":"2025-09-15T08:52:20.442Z"}
{"label":"sikka-repository","level":"info","message":"Finding RequestKey","service":"sikka-service","timestamp":"2025-09-15T08:52:20.443Z","where":{"office_id":"D19408"}}
{"label":"kpis-service","level":"info","message":"Using existing valid request key","service":"sikka-service","timestamp":"2025-09-15T08:52:20.692Z"}
{"label":"sikka-service","level":"info","message":"Calling GET /kpis/treatment_plan_analysis?practice_id=1","service":"sikka-service","timestamp":"2025-09-15T08:52:20.693Z"}
{"error":"Request failed with status code 400","label":"sikka-service","level":"error","message":"Request key API call failed","service":"sikka-service","timestamp":"2025-09-15T08:52:21.787Z"}
{"error":"Sikka API Error: 400 - Bad Request","label":"kpis-service","level":"error","message":"Failed to fetch treatment analysis","service":"sikka-service","timestamp":"2025-09-15T08:52:21.789Z"}
{"error":"Sikka API Error: 400 - Bad Request","level":"error","message":"Failed to fetch treatment analysis","service":"sikka-service","timestamp":"2025-09-15T08:52:21.790Z"}
{"error":"Sikka API Error: 400 - Bad Request","label":"kpis-service","level":"error","message":"Failed to fetch treatment analysis","service":"sikka-service","timestamp":"2025-09-15T08:52:21.790Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-15T08:55:34.694Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-15T08:55:35.545Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-15T08:55:35.550Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-15T08:55:35.551Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-15T08:55:35.552Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-15T08:55:35.553Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-15T08:56:45.366Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-15T08:56:46.553Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-15T08:56:46.558Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-15T08:56:46.559Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-15T08:56:46.560Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-15T08:56:46.561Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-15T08:58:20.451Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-15T08:58:20.842Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-15T08:58:20.847Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-15T08:58:20.847Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-15T08:58:20.848Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-15T08:58:20.849Z"}
{"level":"info","message":"Treatment analysis fetched successfully","service":"sikka-service","timestamp":"2025-09-15T08:59:43.315Z"}
{"label":"kpis-service","level":"info","message":"Treatment analysis fetched successfully","service":"sikka-service","timestamp":"2025-09-15T08:59:43.316Z"}
{"label":"sikka-repository","level":"info","message":"Finding RequestKey","service":"sikka-service","timestamp":"2025-09-15T08:59:43.318Z","where":{"office_id":"D19408"}}
{"label":"kpis-service","level":"info","message":"Using existing valid request key","service":"sikka-service","timestamp":"2025-09-15T08:59:43.547Z"}
{"label":"sikka-service","level":"info","message":"Calling GET /kpis/treatment_plan_analysis?practice_id=1&start_date=2000-09-01&end_date=2025-09-09","service":"sikka-service","timestamp":"2025-09-15T08:59:43.549Z"}
{"error":"Request failed with status code 400","label":"sikka-service","level":"error","message":"Request key API call failed","service":"sikka-service","timestamp":"2025-09-15T08:59:44.682Z"}
{"error":"Sikka API Error: 400 - Bad Request","label":"kpis-service","level":"error","message":"Failed to fetch treatment analysis","service":"sikka-service","timestamp":"2025-09-15T08:59:44.683Z"}
{"error":"Sikka API Error: 400 - Bad Request","level":"error","message":"Failed to fetch treatment analysis","service":"sikka-service","timestamp":"2025-09-15T08:59:44.684Z"}
{"error":"Sikka API Error: 400 - Bad Request","label":"kpis-service","level":"error","message":"Failed to fetch treatment analysis","service":"sikka-service","timestamp":"2025-09-15T08:59:44.683Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-15T09:00:50.576Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-15T09:00:51.730Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-15T09:00:51.736Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-15T09:00:51.737Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-15T09:00:51.737Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-15T09:00:51.738Z"}
{"level":"info","message":"Treatment analysis fetched successfully","service":"sikka-service","timestamp":"2025-09-15T09:01:00.120Z"}
{"label":"kpis-service","level":"info","message":"Treatment analysis fetched successfully","service":"sikka-service","timestamp":"2025-09-15T09:01:00.122Z"}
{"label":"sikka-repository","level":"info","message":"Finding RequestKey","service":"sikka-service","timestamp":"2025-09-15T09:01:00.124Z","where":{"office_id":"D19408"}}
{"label":"kpis-service","level":"info","message":"Using existing valid request key","service":"sikka-service","timestamp":"2025-09-15T09:01:00.167Z"}
{"label":"sikka-service","level":"info","message":"Calling GET /kpis/treatment_plan_analysis?practice_id=1&startdate=2000-09-01&enddate=2025-09-09","service":"sikka-service","timestamp":"2025-09-15T09:01:00.169Z"}
{"label":"sikka-service","level":"info","message":"Treatment analysis fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-15T09:01:01.621Z"}
{"level":"info","message":"Creating TreatmentAnalysis","service":"sikka-service","timestamp":"2025-09-15T09:01:01.623Z"}
{"level":"info","message":"Bulk created 6 sikka_treatment_plan_analysis records","service":"sikka-service","timestamp":"2025-09-15T09:01:01.663Z"}
{"label":"kpis-service","level":"info","message":"Treatment analysis fetched successfully","service":"sikka-service","timestamp":"2025-09-15T09:01:01.664Z"}
{"level":"info","message":"Treatment analysis fetched successfully","service":"sikka-service","timestamp":"2025-09-15T09:01:01.665Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-15T09:01:55.418Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-15T09:01:55.843Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-15T09:01:55.848Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-15T09:01:55.849Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-15T09:01:55.850Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-15T09:01:55.851Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-15T09:05:57.524Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-15T09:05:58.710Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-15T09:05:58.714Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-15T09:05:58.715Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-15T09:05:58.716Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-15T09:05:58.717Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-15T09:06:22.467Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-15T09:06:23.688Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-15T09:06:23.693Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-15T09:06:23.694Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-15T09:06:23.694Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-15T09:06:23.695Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-15T09:33:28.617Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-15T09:33:29.154Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-15T09:33:29.162Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-15T09:33:29.163Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-15T09:33:29.164Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-15T09:33:29.165Z"}
{"level":"info","message":"No show appointments fetched successfully","service":"sikka-service","timestamp":"2025-09-15T09:34:01.380Z"}
{"label":"kpis-service","level":"info","message":"No show appointments fetched successfully","service":"sikka-service","timestamp":"2025-09-15T09:34:01.381Z"}
{"label":"sikka-repository","level":"info","message":"Finding RequestKey","service":"sikka-service","timestamp":"2025-09-15T09:34:01.382Z","where":{"office_id":"D19408"}}
{"label":"kpis-service","level":"info","message":"Using existing valid request key","service":"sikka-service","timestamp":"2025-09-15T09:34:01.687Z"}
{"label":"sikka-service","level":"info","message":"Calling GET /kpis/no_show_appointments?practice_id=1&startdate=2000-09-01&enddate=2025-09-09","service":"sikka-service","timestamp":"2025-09-15T09:34:01.689Z"}
{"label":"sikka-service","level":"info","message":"No show appointments fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-15T09:34:04.132Z"}
{"level":"info","message":"Creating NoShowAppointments","service":"sikka-service","timestamp":"2025-09-15T09:34:04.133Z"}
{"level":"info","message":"Bulk created 1 sikka_no_show_appointment records","service":"sikka-service","timestamp":"2025-09-15T09:34:04.158Z"}
{"label":"kpis-service","level":"info","message":"No show appointments fetched successfully","service":"sikka-service","timestamp":"2025-09-15T09:34:04.159Z"}
{"level":"info","message":"No show appointments fetched successfully","service":"sikka-service","timestamp":"2025-09-15T09:34:04.159Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-15T09:35:47.937Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-15T09:35:48.381Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-15T09:35:48.387Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-15T09:35:48.387Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-15T09:35:48.388Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-15T09:35:48.389Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-15T09:36:12.248Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-15T09:36:12.654Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-15T09:36:12.658Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-15T09:36:12.658Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-15T09:36:12.659Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-15T09:36:12.660Z"}
{"label":"sikka-server","level":"info","message":"SIGINT received, shutting down gracefully","service":"sikka-service","timestamp":"2025-09-15T09:36:15.051Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-15T09:50:13.471Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-15T09:50:13.972Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-15T09:50:13.982Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-15T09:50:13.983Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-15T09:50:13.984Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-15T09:50:13.985Z"}
{"level":"info","message":"Avg daily production fetched successfully","service":"sikka-service","timestamp":"2025-09-15T09:50:45.444Z"}
{"label":"kpis-service","level":"info","message":"Avg daily production fetched successfully","service":"sikka-service","timestamp":"2025-09-15T09:50:45.445Z"}
{"label":"sikka-repository","level":"info","message":"Finding RequestKey","service":"sikka-service","timestamp":"2025-09-15T09:50:45.447Z","where":{"office_id":"D19408"}}
{"label":"kpis-service","level":"info","message":"Using existing valid request key","service":"sikka-service","timestamp":"2025-09-15T09:50:45.655Z"}
{"label":"sikka-service","level":"info","message":"Calling GET /kpis/avg_daily_scheduled_production?practice_id=1&startdate=2000-09-01&enddate=2025-09-09","service":"sikka-service","timestamp":"2025-09-15T09:50:45.657Z"}
{"error":"Request failed with status code 400","label":"sikka-service","level":"error","message":"Request key API call failed","service":"sikka-service","timestamp":"2025-09-15T09:50:46.742Z"}
{"error":"Sikka API Error: 400 - Bad Request","label":"kpis-service","level":"error","message":"Failed to fetch avg daily production","service":"sikka-service","timestamp":"2025-09-15T09:50:46.743Z"}
{"error":"Sikka API Error: 400 - Bad Request","level":"error","message":"Failed to fetch avg daily production","service":"sikka-service","timestamp":"2025-09-15T09:50:46.745Z"}
{"error":"Sikka API Error: 400 - Bad Request","label":"kpis-service","level":"error","message":"Failed to fetch avg daily production","service":"sikka-service","timestamp":"2025-09-15T09:50:46.744Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-15T09:52:49.905Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-15T09:52:50.343Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-15T09:52:50.349Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-15T09:52:50.350Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-15T09:52:50.351Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-15T09:52:50.352Z"}
{"level":"info","message":"Avg daily production fetched successfully","service":"sikka-service","timestamp":"2025-09-15T09:52:59.235Z"}
{"label":"kpis-service","level":"info","message":"Avg daily production fetched successfully","service":"sikka-service","timestamp":"2025-09-15T09:52:59.237Z"}
{"label":"sikka-repository","level":"info","message":"Finding RequestKey","service":"sikka-service","timestamp":"2025-09-15T09:52:59.238Z","where":{"office_id":"D19408"}}
{"label":"kpis-service","level":"info","message":"Using existing valid request key","service":"sikka-service","timestamp":"2025-09-15T09:52:59.311Z"}
{"label":"sikka-service","level":"info","message":"Calling GET /kpis/average_daily_scheduled_production?practice_id=1&startdate=2000-09-01&enddate=2025-09-09","service":"sikka-service","timestamp":"2025-09-15T09:52:59.312Z"}
{"label":"sikka-service","level":"info","message":"Avg daily production fetched successfully","service":"sikka-service","status":200,"timestamp":"2025-09-15T09:53:00.469Z"}
{"level":"info","message":"Creating AvgDailyProduction","service":"sikka-service","timestamp":"2025-09-15T09:53:00.470Z"}
{"level":"info","message":"Bulk created 8 sikka_avg_daily_production records","service":"sikka-service","timestamp":"2025-09-15T09:53:00.502Z"}
{"label":"kpis-service","level":"info","message":"Avg daily production fetched successfully","service":"sikka-service","timestamp":"2025-09-15T09:53:00.502Z"}
{"level":"info","message":"Avg daily production fetched successfully","service":"sikka-service","timestamp":"2025-09-15T09:53:00.503Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-15T09:54:19.530Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-15T09:54:20.412Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-15T09:54:20.416Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-15T09:54:20.417Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-15T09:54:20.417Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-15T09:54:20.418Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-15T09:54:59.264Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-15T09:54:59.713Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-15T09:54:59.719Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-15T09:54:59.720Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-15T09:54:59.721Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-15T09:54:59.722Z"}
{"label":"sikka-server","level":"info","message":"Initializing database connection...","service":"sikka-service","timestamp":"2025-09-15T09:55:32.876Z"}
{"label":"sikka-server","level":"info","message":"Connected to database successfully","service":"sikka-service","timestamp":"2025-09-15T09:55:34.167Z"}
{"label":"sikka-server","level":"info","message":"Sikka service started on port 3003","service":"sikka-service","timestamp":"2025-09-15T09:55:34.172Z"}
{"label":"sikka-server","level":"info","message":"Health check: http://localhost:3003/health","service":"sikka-service","timestamp":"2025-09-15T09:55:34.172Z"}
{"label":"sikka-server","level":"info","message":"API endpoint: http://localhost:3003/api/v1/sikka/request-key","service":"sikka-service","timestamp":"2025-09-15T09:55:34.173Z"}
{"label":"sikka-server","level":"info","message":"Environment: development","service":"sikka-service","timestamp":"2025-09-15T09:55:34.174Z"}
